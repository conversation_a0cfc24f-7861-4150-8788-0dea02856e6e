# Context Worker Python

AutoDev Context Worker 的 Python 实现 - 代码分析和上下文构建工具

## 概述

Context Worker Python 是一个强大的代码分析工具，用于从源代码中提取结构化信息和上下文。它支持多种编程语言，并提供了丰富的分析功能，包括：

- **接口分析** - 提取类、接口、方法等结构信息
- **API分析** - 分析HTTP API端点和调用关系
- **符号分析** - 识别关键代码符号和引用关系
- **作用域图分析** - 构建代码作用域和依赖关系图
- **文档处理** - 从Markdown和ReST文档中提取代码示例

## 特性

### 🚀 多语言支持
- **Python** - 完整的类、函数、变量分析
- **TypeScript/JavaScript** - 类、接口、方法、函数分析
- **Java** - 类、接口、枚举、方法、字段分析
- **扩展性** - 易于添加新语言支持

### 🔍 深度分析
- **Tree-sitter** 基础的精确语法分析
- **作用域图** 构建完整的符号引用关系
- **上下文提取** 保留代码的语义上下文
- **增量分析** 支持大型代码库的高效分析

### 📊 多种输出格式
- **JSON** 结构化数据输出
- **调试信息** 详细的分析过程信息
- **统计报告** 代码复杂度和质量指标

## 安装

### 基础安装

```bash
pip install context-worker-py
```

### 开发环境安装

```bash
git clone https://github.com/autodev/context-worker-py.git
cd context-worker-py
pip install -e ".[dev]"
```

### CLI工具安装

```bash
pip install "context-worker-py[cli]"
```

## 快速开始

### 命令行使用

```bash
# 分析当前目录
context-worker

# 分析指定目录
context-worker --path /path/to/project

# 运行特定分析类型
context-worker --run-interface --skip-api

# 交互式配置
context-worker --interactive
```

### Python API 使用

```python
import asyncio
from context_worker_py import (
    AppConfig,
    AnalysisTypes,
    run
)

async def main():
    config = AppConfig(
        dir_path="/path/to/project",
        analysis_types=AnalysisTypes(
            interface=True,
            api=True,
            symbol=True
        )
    )

    await run(config)

if __name__ == "__main__":
    asyncio.run(main())
```

### 代码分析示例

```python
from context_worker_py import (
    FileSystemScanner,
    CodeCollector,
    SymbolAnalyser,
    ScopeGraph,
    MarkdownAnalyser
)

# 文件系统扫描
scanner = FileSystemScanner()
files = await scanner.scan_directory("/path/to/project")

# 代码收集
collector = CodeCollector()
code_files = await collector.collect_files(files)

# 符号分析
analyser = SymbolAnalyser()
symbols = await analyser.analyse(code_files)

# 作用域图分析
scope_graph = ScopeGraph()
# ... 构建作用域图

# 文档分析
doc_analyser = MarkdownAnalyser()
code_blocks = await doc_analyser.parse(markdown_content)
```

## 配置

### 配置文件

创建 `context_worker_config.json`：

```json
{
  "dir_path": "./",
  "output_dir": "./output",
  "analysis_types": {
    "interface": true,
    "api": true,
    "symbol": true
  },
  "upload": false,
  "base_url": "http://localhost:8080"
}
```

### 环境变量

```bash
export CONTEXT_WORKER_PATH=/path/to/project
export CONTEXT_WORKER_OUTPUT_DIR=./output
export CONTEXT_WORKER_UPLOAD=false
```

## API 文档

### 核心类

#### AppConfig
应用程序配置类，包含所有分析参数。

#### AnalysisTypes
分析类型配置，控制运行哪些分析。

#### FileSystemScanner
文件系统扫描器，用于发现和过滤源代码文件。

#### CodeCollector
代码收集器，负责读取和预处理源代码文件。

#### SymbolAnalyser
符号分析器，提取代码中的符号信息。

#### ScopeGraph
作用域图，表示代码的作用域和引用关系。

### 语言支持

#### LanguageProfile
语言配置基类，定义语言特定的分析规则。

#### PythonProfile
Python 语言配置，包含 Python 特定的语法查询。

#### TypeScriptProfile
TypeScript 语言配置，支持 TypeScript 和 TSX。

#### JavaProfile
Java 语言配置，支持类、接口、枚举等。

## 开发

### 项目结构

```
context_worker_py/
├── types/              # 类型定义
├── base/               # 基础工具和抽象类
├── ast/                # AST 处理
├── model/              # 数据模型
├── analyzer/           # 分析器实现
├── code_context/       # 语言上下文
├── code_search/        # 作用域图分析
├── document/           # 文档处理
├── utils/              # 工具函数
├── test/               # 测试工具
├── cli/                # 命令行接口
└── main.py             # 主入口
```

### 运行测试

```bash
# 安装测试依赖
pip install ".[test]"

# 运行测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=context_worker_py --cov-report=html
```

### 代码格式化

```bash
# 格式化代码
black context_worker_py/
isort context_worker_py/

# 类型检查
mypy context_worker_py/

# 代码检查
flake8 context_worker_py/
```

## 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持 Python、TypeScript、Java 语言分析
- 完整的作用域图分析
- 文档处理功能
- 命令行界面

## 支持

如果您遇到问题或有建议，请：

1. 查看 [文档](https://github.com/autodev/context-worker-py/wiki)
2. 搜索 [已知问题](https://github.com/autodev/context-worker-py/issues)
3. 创建新的 [Issue](https://github.com/autodev/context-worker-py/issues/new)

## 相关项目

- [AutoDev](https://github.com/autodev/autodev) - 主项目
- [Context Worker TypeScript](https://github.com/autodev/context-worker) - TypeScript 版本
# AutoDev Workbench

> **AutoDev Workbench** is an AI-native developer platform designed to accelerate, automate, and contextualize modern
> software development workflows. It serves as your intelligent, unified workspace for building, debugging, learning,
> and collaborating — all powered by large language models.

[![CI/CD Pipeline](https://github.com/unit-mesh/autodev-work/actions/workflows/ci-cd.yml/badge.svg)](https://github.com/unit-mesh/autodev-work/actions/workflows/ci-cd.yml)
![NPM Version](https://img.shields.io/npm/v/%40autodev%2Fcontext-worker)

## Overview

Architecture

![](docs/arch.svg)

Architecture (English)

![](docs/arch-en.svg)

AutoDev Workbench provides a unified platform for development teams to:

- **AI-Powered Development**: Leverage advanced AI tools and models to assist in coding, debugging, and problem-solving
- **Knowledge Management**: Centralize and organize development knowledge, patterns, and best practices
- **Workflow Automation**: Streamline development processes through intelligent automation and pattern recognition
- **Metrics & Analytics**: Track and analyze development performance and productivity metrics
- **API/Components Marketplace**: Access a marketplace of reusable components and libraries to accelerate development
- **Documentation**: Maintain comprehensive documentation with AI-assisted content generation and management

## Usage

### AutoDev Context Web

### AutoDev Context Worker

```bash
npx @autodev/context-worker@latest
```

### Release

```bash
git fetch --tags
pnpm install
pnpm publish-all
# Please do `publish-all` first
pnpm publish-all:confirm
git push --tags
```