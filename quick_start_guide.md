# Context Worker Python 快速使用指南

## 概述

Context Worker Python 是一个强大的代码分析工具，可以帮助你分析代码仓库，提取符号信息，并根据开发任务查找相关的上下文代码。

## 安装

```bash
# 基础安装
pip install context-worker-py

# 包含CLI工具
pip install "context-worker-py[cli]"

# 开发版本安装
git clone https://github.com/autodev/context-worker-py.git
cd context-worker-py
pip install -e ".[dev]"
```

## 快速开始

### 1. 命令行使用

```bash
# 分析当前目录
context-worker

# 分析指定目录
context-worker --path /path/to/your/project

# 运行特定分析类型
context-worker --run-interface --run-symbol --skip-api

# 交互式配置
context-worker --interactive

# 输出到指定文件
context-worker --output-file my_analysis.json
```

### 2. Python API 使用

#### 基础使用

```python
import asyncio
from context_worker_py import (
    AppConfig,
    AnalysisTypes,
    run
)

async def basic_analysis():
    # 创建配置
    config = AppConfig(
        dir_path="/path/to/your/project",
        analysis_types=AnalysisTypes(
            interface=True,    # 接口分析
            api=True,         # API分析
            symbol=True       # 符号分析
        ),
        upload=False,
        output_dir="./analysis_output"
    )
    
    # 运行分析
    await run(config)

# 运行
asyncio.run(basic_analysis())
```

#### 详细使用

```python
import asyncio
from context_worker_py import (
    FileSystemScanner,
    CodeCollector,
    SymbolAnalyser,
    InterfaceAnalyzer
)

async def detailed_analysis():
    project_path = "/path/to/your/project"
    
    # 1. 扫描文件系统
    scanner = FileSystemScanner()
    files = await scanner.scan_directory(project_path)
    print(f"发现 {len(files)} 个文件")
    
    # 2. 收集代码结构
    collector = CodeCollector(project_path)
    # 这里需要解析代码文件并添加到collector
    # collector.add_code_file(file_path, parsed_code_file)
    
    # 3. 符号分析
    symbol_analyser = SymbolAnalyser()
    symbol_result = await symbol_analyser.analyze(collector)
    
    print(f"分析完成，发现 {len(symbol_result.symbols)} 个符号")
    
    # 4. 接口分析
    interface_analyzer = InterfaceAnalyzer()
    interface_result = await interface_analyzer.analyze(collector)
    
    return {
        'symbols': symbol_result,
        'interfaces': interface_result
    }

# 运行详细分析
asyncio.run(detailed_analysis())
```

## 核心功能

### 1. 文件系统扫描

```python
from context_worker_py import FileSystemScanner

scanner = FileSystemScanner()

# 扫描目录
files = await scanner.scan_directory("/path/to/project")

# 添加自定义忽略模式
scanner.add_ignore_pattern("*.tmp")

# 读取文件内容
content = await scanner.read_file_content("file.py")
```

### 2. 代码收集和分析

```python
from context_worker_py import CodeCollector, SymbolAnalyser

# 创建代码收集器
collector = CodeCollector("/path/to/project")

# 添加代码文件（需要先解析）
# collector.add_code_file(file_path, code_file)

# 符号分析
analyser = SymbolAnalyser()
result = await analyser.analyze(collector)

# 获取分析结果
for symbol in result.symbols:
    print(f"符号: {symbol.name} ({symbol.kind})")
    print(f"位置: {symbol.file_path}:{symbol.position.start.row}")
```

### 3. 查找相关上下文

```python
def find_related_context(task_description: str, analysis_result):
    """根据任务描述查找相关代码"""
    keywords = task_description.lower().split()
    related_contexts = []
    
    for symbol in analysis_result.symbols:
        symbol_name = symbol.name.lower()
        if any(keyword in symbol_name for keyword in keywords):
            related_contexts.append({
                'symbol': symbol.name,
                'file': symbol.file_path,
                'position': symbol.position,
                'reason': f"包含关键词: {keywords}"
            })
    
    return related_contexts

# 使用示例
task = "用户认证功能"
contexts = find_related_context(task, symbol_result)
```

## 配置选项

### AppConfig 参数

- `dir_path`: 要扫描的目录路径
- `analysis_types`: 分析类型配置
  - `interface`: 是否进行接口分析
  - `api`: 是否进行API分析  
  - `symbol`: 是否进行符号分析
- `upload`: 是否上传结果到服务器
- `base_url`: 服务器地址
- `output_dir`: 输出目录
- `output_json_file`: JSON结果文件名
- `project_id`: 项目ID（可选）

### 配置文件

创建 `context_worker_config.json`:

```json
{
  "dir_path": "./",
  "output_dir": "./output",
  "analysis_types": {
    "interface": true,
    "api": true,
    "symbol": true
  },
  "upload": false,
  "base_url": "http://localhost:8080"
}
```

## 实际使用场景

### 场景1: 新功能开发前的代码调研

```python
async def research_for_new_feature():
    """为新功能开发进行代码调研"""
    
    # 分析现有代码库
    config = AppConfig(
        dir_path="./src",
        analysis_types=AnalysisTypes(symbol=True, interface=True)
    )
    
    await run(config)
    
    # 查找相关的现有实现
    task = "支付功能模块"
    # 基于分析结果查找相关代码...
```

### 场景2: 代码重构前的影响分析

```python
async def analyze_refactoring_impact():
    """分析重构的影响范围"""
    
    # 全面分析代码结构
    scanner = FileSystemScanner()
    files = await scanner.scan_directory("./")
    
    # 分析符号依赖关系
    # 识别需要修改的文件和接口
```

### 场景3: 代码审查和质量分析

```python
async def code_review_analysis():
    """代码审查和质量分析"""
    
    config = AppConfig(
        dir_path="./",
        analysis_types=AnalysisTypes(
            interface=True,
            symbol=True,
            api=True
        )
    )
    
    result = await run(config)
    # 分析代码质量指标
    # 检查接口一致性
    # 识别潜在问题
```

## 注意事项

1. **文件大小限制**: 默认跳过大于1MB的文件
2. **忽略模式**: 自动忽略 `.git`、`node_modules` 等目录
3. **编码支持**: 支持UTF-8和Latin-1编码
4. **语言支持**: 目前支持Python、TypeScript、Java等主流语言
5. **性能考虑**: 大型项目建议分批处理或使用过滤器

## 故障排除

### 常见问题

1. **文件扫描失败**: 检查目录权限和路径是否正确
2. **符号分析错误**: 确保代码文件语法正确
3. **内存不足**: 对于大型项目，考虑增加内存或分批处理

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
config = AppConfig(
    dir_path="./",
    # ... 其他配置
)
```

## 扩展和自定义

### 自定义分析器

```python
from context_worker_py import ICodeAnalyzer

class CustomAnalyzer(ICodeAnalyzer):
    async def analyze(self, code_collector):
        # 实现自定义分析逻辑
        pass
```

### 自定义忽略模式

```python
scanner = FileSystemScanner()
scanner.add_ignore_pattern("*.backup")
scanner.add_ignore_pattern("temp_*")
```

这个指南应该能帮助你快速上手使用 Context Worker Python 来分析代码仓库和查找相关上下文代码。
