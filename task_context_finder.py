#!/usr/bin/env python3
"""
任务上下文查找器

这个脚本演示如何使用 context_worker_py 来查找与特定开发任务相关的代码上下文。
"""

import asyncio
import json
import os
import sys
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

# 导入 context_worker_py 组件
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from context_worker_py import (
    FileSystemScanner,
    CodeCollector,
    SymbolAnalyser,
    AppConfig,
    AnalysisTypes,
    run
)


@dataclass
class TaskContext:
    """任务上下文结果"""
    task_description: str
    related_files: List[str]
    related_symbols: List[Dict[str, Any]]
    confidence_score: float
    suggestions: List[str]


class TaskContextFinder:
    """任务上下文查找器"""
    
    def __init__(self, project_path: str):
        self.project_path = os.path.abspath(project_path)
        self.scanner = FileSystemScanner()
        self.collector = CodeCollector(self.project_path)
        self.symbol_analyser = SymbolAnalyser()
        
        # 预定义的任务关键词映射
        self.task_keywords = {
            "用户认证": ["auth", "login", "user", "password", "token", "session"],
            "文件上传": ["upload", "file", "multipart", "storage", "blob"],
            "数据库操作": ["db", "database", "sql", "query", "model", "entity"],
            "API接口": ["api", "endpoint", "route", "controller", "service"],
            "缓存功能": ["cache", "redis", "memory", "store", "expire"],
            "日志记录": ["log", "logger", "audit", "trace", "debug"],
            "配置管理": ["config", "setting", "env", "property", "option"],
            "错误处理": ["error", "exception", "try", "catch", "handle"],
            "测试相关": ["test", "spec", "mock", "assert", "verify"],
            "安全功能": ["security", "encrypt", "decrypt", "hash", "validate"],
            "补丁管理": ["patch", "update", "version", "deploy", "install"],
            "删除操作": ["delete", "remove", "drop", "clear", "clean"]
        }
    
    def extract_keywords(self, task_description: str) -> List[str]:
        """从任务描述中提取关键词"""
        keywords = []
        task_lower = task_description.lower()
        
        # 检查预定义的任务类型
        for task_type, type_keywords in self.task_keywords.items():
            if task_type in task_description or any(kw in task_lower for kw in type_keywords):
                keywords.extend(type_keywords)
        
        # 添加任务描述中的词汇
        words = task_description.lower().split()
        keywords.extend([word.strip('.,!?;:') for word in words if len(word) > 2])
        
        return list(set(keywords))  # 去重
    
    async def scan_project_files(self) -> List[str]:
        """扫描项目文件"""
        print(f"🔍 扫描项目目录: {self.project_path}")
        
        try:
            files = await self.scanner.scan_directory(self.project_path)
            print(f"📁 发现 {len(files)} 个文件")
            return files
        except Exception as e:
            print(f"❌ 扫描失败: {e}")
            return []
    
    def filter_relevant_files(self, files: List[str], keywords: List[str]) -> List[str]:
        """根据关键词过滤相关文件"""
        relevant_files = []
        
        for file_path in files:
            file_name = os.path.basename(file_path).lower()
            file_dir = os.path.dirname(file_path).lower()
            
            # 检查文件名和目录名是否包含关键词
            if any(keyword in file_name or keyword in file_dir for keyword in keywords):
                relevant_files.append(file_path)
        
        return relevant_files
    
    async def analyze_file_content(self, file_path: str, keywords: List[str]) -> Dict[str, Any]:
        """分析文件内容的相关性"""
        try:
            content = await self.scanner.read_file_content(file_path)
            content_lower = content.lower()
            
            # 计算关键词匹配度
            matches = []
            for keyword in keywords:
                count = content_lower.count(keyword)
                if count > 0:
                    matches.append({
                        'keyword': keyword,
                        'count': count,
                        'lines': self._find_keyword_lines(content, keyword)
                    })
            
            # 计算相关性分数
            relevance_score = sum(match['count'] for match in matches) / len(content.split('\n'))
            
            return {
                'file_path': file_path,
                'matches': matches,
                'relevance_score': relevance_score,
                'total_lines': len(content.split('\n'))
            }
        except Exception as e:
            return {
                'file_path': file_path,
                'error': str(e),
                'relevance_score': 0
            }
    
    def _find_keyword_lines(self, content: str, keyword: str) -> List[int]:
        """查找关键词所在的行号"""
        lines = content.split('\n')
        keyword_lines = []
        
        for i, line in enumerate(lines, 1):
            if keyword.lower() in line.lower():
                keyword_lines.append(i)
        
        return keyword_lines
    
    async def find_task_context(self, task_description: str) -> TaskContext:
        """查找任务相关的代码上下文"""
        print(f"🎯 查找任务上下文: {task_description}")
        print("=" * 50)
        
        # 1. 提取关键词
        keywords = self.extract_keywords(task_description)
        print(f"🔑 提取的关键词: {keywords}")
        
        # 2. 扫描项目文件
        all_files = await self.scan_project_files()
        
        # 3. 过滤相关文件
        relevant_files = self.filter_relevant_files(all_files, keywords)
        print(f"📄 相关文件数量: {len(relevant_files)}")
        
        # 4. 分析文件内容
        file_analyses = []
        for file_path in relevant_files[:20]:  # 限制分析文件数量
            analysis = await self.analyze_file_content(file_path, keywords)
            if analysis.get('relevance_score', 0) > 0:
                file_analyses.append(analysis)
        
        # 5. 按相关性排序
        file_analyses.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
        
        # 6. 生成建议
        suggestions = self._generate_suggestions(task_description, file_analyses, keywords)
        
        # 7. 计算整体置信度
        confidence_score = self._calculate_confidence(file_analyses, keywords)
        
        return TaskContext(
            task_description=task_description,
            related_files=[analysis['file_path'] for analysis in file_analyses],
            related_symbols=[],  # 这里可以扩展符号分析
            confidence_score=confidence_score,
            suggestions=suggestions
        )
    
    def _generate_suggestions(self, task_description: str, file_analyses: List[Dict], keywords: List[str]) -> List[str]:
        """生成开发建议"""
        suggestions = []
        
        if not file_analyses:
            suggestions.append("未找到直接相关的代码文件，建议从头开始实现")
            suggestions.append(f"可以参考类似功能的实现模式")
            return suggestions
        
        # 基于分析结果生成建议
        top_files = file_analyses[:3]
        suggestions.append(f"建议重点关注以下文件:")
        
        for i, analysis in enumerate(top_files, 1):
            file_name = os.path.basename(analysis['file_path'])
            score = analysis.get('relevance_score', 0)
            suggestions.append(f"  {i}. {file_name} (相关性: {score:.3f})")
        
        # 基于关键词匹配生成建议
        common_keywords = set()
        for analysis in file_analyses:
            for match in analysis.get('matches', []):
                common_keywords.add(match['keyword'])
        
        if common_keywords:
            suggestions.append(f"发现常用关键词: {', '.join(list(common_keywords)[:5])}")
            suggestions.append("建议保持命名一致性")
        
        return suggestions
    
    def _calculate_confidence(self, file_analyses: List[Dict], keywords: List[str]) -> float:
        """计算置信度分数"""
        if not file_analyses:
            return 0.0
        
        # 基于文件数量和相关性分数计算
        total_score = sum(analysis.get('relevance_score', 0) for analysis in file_analyses)
        file_count_factor = min(len(file_analyses) / 10, 1.0)  # 文件数量因子
        keyword_factor = min(len(keywords) / 5, 1.0)  # 关键词因子
        
        confidence = (total_score * file_count_factor * keyword_factor) * 100
        return min(confidence, 100.0)
    
    def print_results(self, context: TaskContext) -> None:
        """打印分析结果"""
        print("\n" + "=" * 50)
        print("📊 任务上下文分析结果")
        print("=" * 50)
        
        print(f"📋 任务描述: {context.task_description}")
        print(f"🎯 置信度: {context.confidence_score:.1f}%")
        print(f"📁 相关文件数: {len(context.related_files)}")
        
        if context.related_files:
            print("\n📄 相关文件列表:")
            for i, file_path in enumerate(context.related_files[:10], 1):
                rel_path = os.path.relpath(file_path, self.project_path)
                print(f"  {i}. {rel_path}")
        
        if context.suggestions:
            print("\n💡 开发建议:")
            for suggestion in context.suggestions:
                print(f"  • {suggestion}")
    
    async def save_results(self, context: TaskContext, output_file: str) -> None:
        """保存结果到文件"""
        result_data = {
            'task_description': context.task_description,
            'confidence_score': context.confidence_score,
            'related_files': [os.path.relpath(f, self.project_path) for f in context.related_files],
            'suggestions': context.suggestions,
            'analysis_timestamp': str(asyncio.get_event_loop().time())
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 结果已保存到: {output_file}")


async def main():
    """主函数"""
    
    project_path = "/home/<USER>/fengbin/DailyWork/ZeroAgents/test_repo/java_repo"
    task_description = """
# **用户故事**

作为运维人员，我希望能够通过新增的API接口删除指定名称的单个补丁

验收准则：

​    **Scenario 1: 成功删除未应用的补丁**

​    Given: 存在一个名为 "patch_v1.0" 的补丁

​    And: 该补丁未出现在补丁历史记录中 (未应用过)

​    When: 调用删除单个补丁接口

​    Then:删除补丁详情表中指定补丁

​    And: 删除补丁分发表中指定补丁



​    **Scenario 2: 无法删除已应用的补丁**

​    Given: 存在一个名为 "patch_v2.0" 的补丁

​    And: 该补丁已出现在补丁历史记录中 (已应用过)

​    When: 调用删除单个补丁接口

​    Then: 抛出异常，提示 "patch has updated, patchName=patch_v2.0"

​    And: 所有服务中的补丁信息保持不变


# 业务流程


 ```
@startuml
actor 用户 as user
participant "补丁删除服务" as patcherService
participant "数据库" as Database

user -> patcherService: 调用删除补丁接口

group 前置校验
    patcherService -> Database: 查询补丁历史记录
    Database --> patcherService: 返回历史记录
    alt 补丁已应用
        patcherService --> user: 抛出异常("补丁已升级")
    else 未升级
         patcherService -> Database: 删除补丁详情记录
         patcherService -> Database: 删除补丁分发记录
    end
end

patcherService --> user: 返回操作结果
@enduml
 ```

# 接口定义

```json
{
  "swagger": "2.0",
  "info": {
    "version": "1.0.0",
    "title": "补丁删除API",
    "description": "提供补丁删除操作的接口"
  },
  "host": "api.example.com",
  "basePath": "/",
  "schemes": [
    "https"
  ],
  "consumes": [
    "application/json"
  ],
  "produces": [
    "application/json"
  ],
  "paths": {
    "/patches/delete/singlePatch": {
      "post": {
        "tags": [
          "补丁删除"
        ],
        "summary": "删除单个补丁",
        "description": "删除单个补丁",
        "operationId": "deleteSinglePatch",
        "parameters": [
          {
            "name": "patchName",
            "in": "query",
            "description": "补丁名称",
            "required": true,
            "type": "string",
            "example": "patch-1.0.0"
          }
        ],
        "responses": {
          "200": {
            "description": "删除成功",
            "schema": {
              "type": "string",
              "example": "success"
            }
          },
          "400": {
            "description": "参数错误",
            "schema": {
              "type": "string",
              "example": "参数错误"
            }
          },
          "500": {
            "description": "服务器内部错误",
            "schema": {
              "type": "string",
              "example": "服务器内部错误"
            }
          }
        }
      }
    }
  }
}
```
"""
    
    if not os.path.exists(project_path):
        print(f"❌ 项目路径不存在: {project_path}")
        sys.exit(1)
    
    # 创建查找器
    finder = TaskContextFinder(project_path)
    
    try:
        # 查找任务上下文
        context = await finder.find_task_context(task_description)
        
        # 打印结果
        finder.print_results(context)
        
        # 保存结果
        output_file = f"task_context_{task_description.replace(' ', '_')}.json"
        await finder.save_results(context, output_file)
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
