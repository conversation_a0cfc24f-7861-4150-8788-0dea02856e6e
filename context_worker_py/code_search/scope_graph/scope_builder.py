"""
Scope graph builder for constructing scope graphs from tree-sitter ASTs
"""

import asyncio
from typing import Dict, List, Optional, TYPE_CHECKING

try:
    from tree_sitter import Query, Node
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    Query = None
    Node = None

from .scope_graph import ScopeGraph, NodeId
from .model.text_range import TextRange
from .model.symbol_id import SymbolId
from .model.symbol import Symbol
from .node.local_scope import LocalScope
from .node.local_def import LocalDef
from .node.local_import import LocalImport
from .node.reference import Reference
from .edge.edge_kind import DefToScope, ImportToScope, RefToDef, RefToImport, ScopeToScope

if TYPE_CHECKING:
    from ...code_context.base.language_profile import LanguageProfile


class ScopeBuilder:
    """
    Builder for constructing scope graphs from tree-sitter ASTs.

    This class processes tree-sitter query results to build a scope graph
    that represents the scopes, definitions, references, and their relationships
    in a source code file.
    """

    def __init__(self, query: 'Query', root_node: 'Node', source_code: str,
                 language_profile: 'LanguageProfile'):
        if not HAS_TREE_SITTER:
            raise ImportError("tree-sitter is required for scope graph building")

        self.query = query
        self.root_node = root_node
        self.source_code = source_code
        self.language_profile = language_profile

        # Build state
        self.scope_graph = ScopeGraph()
        self.scope_stack: List[NodeId] = []
        self.symbol_table: Dict[str, SymbolId] = {}
        self.current_namespace_index = 0

        # Initialize namespaces
        self.scope_graph.namespaces = [[]]  # Start with one empty namespace

    async def build(self) -> ScopeGraph:
        """Build the scope graph from the AST"""
        if not self.query or not self.root_node:
            return self.scope_graph

        try:
            # Execute the scope query
            matches = self.query.matches(self.root_node)

            # Process matches to build scope graph
            await self._process_matches(matches)

            # Post-process to resolve references
            await self._resolve_references()

        except Exception as e:
            print(f"Error building scope graph: {e}")

        return self.scope_graph

    async def _process_matches(self, matches) -> None:
        """Process tree-sitter query matches"""
        for match in matches:
            await self._process_match(match)

    async def _process_match(self, match) -> None:
        """Process a single query match"""
        for capture in match.captures:
            node = capture.node
            capture_name = capture.name

            # Create text range for the node
            text_range = TextRange.from_node(node)

            if capture_name == 'scope':
                await self._handle_scope(node, text_range)
            elif capture_name == 'definition':
                await self._handle_definition(node, text_range)
            elif capture_name == 'reference':
                await self._handle_reference(node, text_range)
            elif capture_name == 'import':
                await self._handle_import(node, text_range)

    async def _handle_scope(self, node: 'Node', text_range: TextRange) -> None:
        """Handle a scope node"""
        # Create scope node
        scope_kind = LocalScope(text_range)
        scope_id = self.scope_graph.add_node(scope_kind)

        # Connect to parent scope if exists
        if self.scope_stack:
            parent_scope_id = self.scope_stack[-1]
            self.scope_graph.add_edge(scope_id, parent_scope_id, ScopeToScope())

        # Push to scope stack
        self.scope_stack.append(scope_id)

        # Process child nodes
        await self._process_children(node)

        # Pop from scope stack
        self.scope_stack.pop()

    async def _handle_definition(self, node: 'Node', text_range: TextRange) -> None:
        """Handle a definition node"""
        symbol_name = self._extract_symbol_name(node)
        if not symbol_name:
            return

        # Get or create symbol ID
        symbol_id = self._get_or_create_symbol_id(symbol_name)

        # Create definition node
        def_kind = LocalDef(text_range, symbol_id)
        def_id = self.scope_graph.add_node(def_kind)

        # Connect to current scope
        if self.scope_stack:
            current_scope_id = self.scope_stack[-1]
            self.scope_graph.add_edge(def_id, current_scope_id, DefToScope())

        # Add as symbol
        symbol = Symbol(kind='definition', range=text_range)
        self.scope_graph.add_symbol(symbol)

    async def _handle_reference(self, node: 'Node', text_range: TextRange) -> None:
        """Handle a reference node"""
        symbol_name = self._extract_symbol_name(node)
        if not symbol_name:
            return

        # Get or create symbol ID
        symbol_id = self._get_or_create_symbol_id(symbol_name)

        # Create reference node
        ref_kind = Reference(text_range, symbol_id)
        ref_id = self.scope_graph.add_node(ref_kind)

        # Add as symbol
        symbol = Symbol(kind='reference', range=text_range)
        self.scope_graph.add_symbol(symbol)

    async def _handle_import(self, node: 'Node', text_range: TextRange) -> None:
        """Handle an import node"""
        symbol_name = self._extract_symbol_name(node)
        if not symbol_name:
            return

        # Get or create symbol ID
        symbol_id = self._get_or_create_symbol_id(symbol_name)

        # Create import node
        import_kind = LocalImport(text_range, symbol_id)
        import_id = self.scope_graph.add_node(import_kind)

        # Connect to current scope
        if self.scope_stack:
            current_scope_id = self.scope_stack[-1]
            self.scope_graph.add_edge(import_id, current_scope_id, ImportToScope())

        # Add as symbol
        symbol = Symbol(kind='import', range=text_range)
        self.scope_graph.add_symbol(symbol)

    async def _process_children(self, node: 'Node') -> None:
        """Process child nodes recursively"""
        if not HAS_TREE_SITTER or not node:
            return

        for child in node.children:
            # Create text range for child
            child_range = TextRange.from_node(child)

            # Process based on node type
            if child.type in ['function_definition', 'class_definition', 'block']:
                await self._handle_scope(child, child_range)
            elif child.type in ['identifier', 'variable_declarator']:
                # Could be definition or reference - need more context
                await self._handle_potential_definition_or_reference(child, child_range)
            else:
                # Recursively process other children
                await self._process_children(child)

    async def _handle_potential_definition_or_reference(self, node: 'Node', text_range: TextRange) -> None:
        """Handle nodes that could be definitions or references"""
        # This is a simplified heuristic - in practice, you'd need more sophisticated analysis
        parent = node.parent
        if parent:
            if parent.type in ['assignment', 'variable_declaration', 'function_definition']:
                await self._handle_definition(node, text_range)
            else:
                await self._handle_reference(node, text_range)

    def _extract_symbol_name(self, node: 'Node') -> str:
        """Extract symbol name from a tree-sitter node"""
        if not HAS_TREE_SITTER or not node:
            return ""

        try:
            if hasattr(node, 'text'):
                return node.text.decode('utf-8')
            return ""
        except (UnicodeDecodeError, AttributeError):
            return ""

    def _get_or_create_symbol_id(self, symbol_name: str) -> SymbolId:
        """Get or create a symbol ID for the given symbol name"""
        if symbol_name in self.symbol_table:
            return self.symbol_table[symbol_name]

        # Add to current namespace
        current_namespace = self.scope_graph.namespaces[self.current_namespace_index]
        symbol_index = len(current_namespace)
        current_namespace.append(symbol_name)

        # Create symbol ID
        symbol_id = SymbolId(
            namespace_index=self.current_namespace_index,
            symbol_index=symbol_index
        )

        # Cache it
        self.symbol_table[symbol_name] = symbol_id

        return symbol_id

    async def _resolve_references(self) -> None:
        """Resolve references to their definitions or imports"""
        # Find all reference nodes
        ref_nodes = self.scope_graph.find_nodes(Reference)

        for ref_id in ref_nodes:
            ref_node = self.scope_graph.get_node(ref_id)
            if not ref_node or not isinstance(ref_node.kind, Reference):
                continue

            symbol_id = ref_node.kind.symbol_id

            # Find definitions with the same symbol ID
            def_nodes = self.scope_graph.find_definitions(symbol_id)
            for def_id in def_nodes:
                self.scope_graph.add_edge(ref_id, def_id, RefToDef())

            # Find imports with the same symbol ID
            import_nodes = self.scope_graph.find_imports(symbol_id)
            for import_id in import_nodes:
                self.scope_graph.add_edge(ref_id, import_id, RefToImport())