[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "context-worker-py"
version = "0.1.0"
description = "Python port of AutoDev Context Worker - Code analysis and context extraction library"
authors = [
    {name = "AutoDev Team", email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "tree-sitter>=0.20.0",
    "tree-sitter-languages>=1.8.0",
    "pyyaml>=6.0",
    "click>=8.0.0",
    "requests>=2.28.0",
    "beautifulsoup4>=4.11.0",
    "markdown>=3.4.0",
    "gitpython>=3.1.0",
    "chardet>=5.0.0",
    "pathspec>=0.10.0",
    "typing-extensions>=4.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=22.0.0",
    "isort>=5.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
]
cli = [
    "inquirer>=3.0.0",
    "colorama>=0.4.0",
    "rich>=13.0.0",
]
docs = [
    "sphinx>=6.0.0",
    "sphinx-rtd-theme>=1.2.0",
    "myst-parser>=1.0.0",
]

[project.scripts]
context-worker = "context_worker_py.main:main"
context-worker-finder = "context_worker_py.examples.task_context_finder:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["context_worker_py*"]

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true