"""
Main entry point for context worker
"""

import asyncio
import sys
from typing import Optional

from .types.app_config import AppConfig
from .cli.cli import CommandLineParser, UserInputHandler


async def run(options: Optional[AppConfig] = None) -> None:
    """
    Main entry point for the context worker application

    Args:
        options: Optional configuration to override command line arguments
    """
    try:
        # Parse command line arguments
        command_line_parser = CommandLineParser()
        user_input_handler = UserInputHandler()

        cmd_config = command_line_parser.parse()

        # Merge with provided options
        initial_config = AppConfig(
            dir_path=options.dir_path if options and options.dir_path else cmd_config.dir_path,
            upload=options.upload if options and options.upload is not None else cmd_config.upload,
            base_url=options.base_url if options and options.base_url else cmd_config.base_url,
            output_dir=options.output_dir if options and options.output_dir else cmd_config.output_dir,
            non_interactive=options.non_interactive if options and options.non_interactive is not None else cmd_config.non_interactive,
            context_type=options.context_type if options and options.context_type else cmd_config.context_type,
            output_json_file=options.output_json_file if options and options.output_json_file else cmd_config.output_json_file,
            project_id=options.project_id if options and options.project_id else cmd_config.project_id,
            analysis_types=options.analysis_types if options and options.analysis_types else cmd_config.analysis_types
        )

        config = initial_config

        # Check if we should prompt for interactive input
        is_default_path = cmd_config.dir_path == initial_config.dir_path
        should_prompt = not config.non_interactive and (is_default_path and not (options and options.dir_path))

        if should_prompt:
            config = await user_input_handler.get_app_config(config)

        print(f"Configuration: {config}")

        # TODO: Initialize and run the analyzer app
        # This would be implemented when the analyzer module is converted
        print("Context worker analysis would run here...")

        # Placeholder for analysis logic
        if config.analysis_types['interface']:
            print("正在运行接口分析...")
            # await app.handle_interface_context()

        if config.analysis_types['api']:
            print("正在运行API分析...")
            # await app.handle_http_api_context()

        if config.analysis_types['symbol']:
            print("正在运行关键代码标识...")
            # await app.handle_symbol_context()

        if not any([config.analysis_types['interface'], config.analysis_types['api'], config.analysis_types['symbol']]):
            print("没有选择任何分析类型，默认运行所有分析...")
            # Run all analyses

        print("分析完成!")

    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


def main() -> None:
    """Synchronous main entry point"""
    asyncio.run(run())


if __name__ == "__main__":
    main()