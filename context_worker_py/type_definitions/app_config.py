"""
Application configuration types and defaults
"""

import os
from dataclasses import dataclass
from typing import Optional, Literal
from typing_extensions import TypedDict


class AnalysisTypes(TypedDict):
    """Analysis types configuration"""
    interface: bool
    api: bool
    symbol: bool


@dataclass
class AppConfig:
    """Application unified configuration interface"""

    # Directory path to scan
    dir_path: str

    # Whether to upload analysis results to server
    upload: bool

    # Server address
    base_url: str

    # Learning materials output directory
    output_dir: str

    # Whether to use non-interactive mode
    non_interactive: bool

    # Context type to process
    context_type: Literal['api', 'interface']

    # JSON result output filename
    output_json_file: Optional[str] = None

    # Project ID
    project_id: Optional[str] = None

    # Analysis types to run
    analysis_types: AnalysisTypes = None

    def __post_init__(self):
        """Initialize default analysis types if not provided"""
        if self.analysis_types is None:
            self.analysis_types = AnalysisTypes(
                interface=True,
                api=True,
                symbol=True
            )


# Application default configuration
DEFAULT_CONFIG = AppConfig(
    dir_path=os.getcwd(),
    upload=False,
    base_url='http://localhost:3000/',
    output_dir='materials',
    non_interactive=False,
    context_type='api',
    output_json_file='analysis_result.json',
    project_id=None,
    analysis_types=AnalysisTypes(
        interface=True,
        api=True,
        symbol=True
    )
)