#!/usr/bin/env python3
"""
Context Worker Python 包测试脚本

这个脚本测试 context_worker_py 包的基本功能和导入。
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))

def test_basic_imports():
    """测试基础导入"""
    print("🔍 测试基础导入...")
    
    try:
        # 测试核心组件导入
        from context_worker_py import (
            AppConfig,
            AnalysisTypes,
            FileSystemScanner,
            CodeCollector,
            SymbolAnalyser,
            run
        )
        print("✅ 核心组件导入成功")
        
        # 测试示例导入
        from context_worker_py.examples import (
            ContextWorkerExample,
            TaskContextFinder,
            TaskContext
        )
        print("✅ 示例组件导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_basic_functionality():
    """测试基础功能"""
    print("\n🧪 测试基础功能...")
    
    try:
        from context_worker_py import FileSystemScanner, CodeCollector
        
        # 测试文件扫描器
        scanner = FileSystemScanner()
        print("✅ FileSystemScanner 创建成功")
        
        # 测试代码收集器
        collector = CodeCollector(".")
        print("✅ CodeCollector 创建成功")
        
        # 测试配置
        from context_worker_py import AppConfig, AnalysisTypes
        config = AppConfig(
            dir_path=".",
            analysis_types=AnalysisTypes(
                interface=True,
                api=True,
                symbol=True
            ),
            upload=False,
            base_url="http://localhost:3000",
            output_dir="./test_output",
            non_interactive=True,
            context_type="interface"
        )
        print("✅ AppConfig 创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False


def test_examples():
    """测试示例功能"""
    print("\n📝 测试示例功能...")
    
    try:
        from context_worker_py.examples import ContextWorkerExample, TaskContextFinder
        
        # 测试 ContextWorkerExample
        example = ContextWorkerExample(".")
        print("✅ ContextWorkerExample 创建成功")
        
        # 测试 TaskContextFinder
        finder = TaskContextFinder(".")
        print("✅ TaskContextFinder 创建成功")
        
        # 测试关键词提取
        keywords = finder.extract_keywords("用户认证功能")
        print(f"✅ 关键词提取成功: {keywords[:5]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 示例测试失败: {e}")
        return False


def test_package_structure():
    """测试包结构"""
    print("\n📦 测试包结构...")
    
    required_modules = [
        "analyzer",
        "ast", 
        "base",
        "cli",
        "code_context",
        "code_search",
        "codemodel",
        "document",
        "examples",
        "types",
        "utils"
    ]
    
    missing_modules = []
    
    for module in required_modules:
        module_path = os.path.join(os.path.dirname(__file__), module)
        if not os.path.exists(module_path):
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少模块: {missing_modules}")
        return False
    else:
        print("✅ 包结构完整")
        return True


def main():
    """主测试函数"""
    print("🧪 Context Worker Python 包测试")
    print("=" * 50)
    
    tests = [
        ("基础导入", test_basic_imports),
        ("基础功能", test_basic_functionality), 
        ("示例功能", test_examples),
        ("包结构", test_package_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 运行测试: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！包已准备就绪。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查包配置。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
