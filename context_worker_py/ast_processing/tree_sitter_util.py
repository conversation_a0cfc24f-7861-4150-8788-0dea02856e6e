"""
Tree-sitter utility functions
"""

from typing import List, Optional

try:
    from tree_sitter import Node
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    Node = None


class TreeSitterUtil:
    """Utility functions for working with tree-sitter nodes"""

    @staticmethod
    def previous_nodes_of_type(node: 'Node', node_types: List[str]) -> List['Node']:
        """
        Find previous sibling nodes of specified types

        Args:
            node: The reference node
            node_types: List of node types to search for

        Returns:
            List of matching previous sibling nodes
        """
        if not HAS_TREE_SITTER or not node:
            return []

        result = []
        current = node.prev_sibling

        while current:
            if current.type in node_types:
                result.append(current)
                break  # Only get the immediate previous comment
            current = current.prev_sibling

        return result

    @staticmethod
    def next_nodes_of_type(node: 'Node', node_types: List[str]) -> List['Node']:
        """
        Find next sibling nodes of specified types

        Args:
            node: The reference node
            node_types: List of node types to search for

        Returns:
            List of matching next sibling nodes
        """
        if not HAS_TREE_SITTER or not node:
            return []

        result = []
        current = node.next_sibling

        while current:
            if current.type in node_types:
                result.append(current)
                break  # Only get the immediate next node
            current = current.next_sibling

        return result

    @staticmethod
    def find_child_of_type(node: 'Node', node_type: str) -> Optional['Node']:
        """
        Find first child node of specified type

        Args:
            node: The parent node
            node_type: The type of child node to find

        Returns:
            First matching child node or None
        """
        if not HAS_TREE_SITTER or not node:
            return None

        for child in node.children:
            if child.type == node_type:
                return child

        return None

    @staticmethod
    def find_children_of_type(node: 'Node', node_type: str) -> List['Node']:
        """
        Find all child nodes of specified type

        Args:
            node: The parent node
            node_type: The type of child nodes to find

        Returns:
            List of matching child nodes
        """
        if not HAS_TREE_SITTER or not node:
            return []

        return [child for child in node.children if child.type == node_type]

    @staticmethod
    def get_node_text(node: 'Node') -> str:
        """
        Get text content of a node

        Args:
            node: The tree-sitter node

        Returns:
            Text content as string
        """
        if not HAS_TREE_SITTER or not node:
            return ""

        try:
            if hasattr(node, 'text'):
                return node.text.decode('utf-8')
            return ""
        except (UnicodeDecodeError, AttributeError):
            return ""

    @staticmethod
    def walk_tree(node: 'Node', callback, depth: int = 0) -> None:
        """
        Walk through tree nodes and apply callback

        Args:
            node: Starting node
            callback: Function to call for each node
            depth: Current depth in the tree
        """
        if not HAS_TREE_SITTER or not node:
            return

        callback(node, depth)

        for child in node.children:
            TreeSitterUtil.walk_tree(child, callback, depth + 1)

    @staticmethod
    def find_nodes_by_type(root: 'Node', node_type: str) -> List['Node']:
        """
        Find all nodes of specified type in the tree

        Args:
            root: Root node to search from
            node_type: Type of nodes to find

        Returns:
            List of matching nodes
        """
        if not HAS_TREE_SITTER or not root:
            return []

        result = []

        def collect_nodes(node: 'Node', depth: int) -> None:
            if node.type == node_type:
                result.append(node)

        TreeSitterUtil.walk_tree(root, collect_nodes)
        return result