"""
Tree-sitter file representation and parsing
"""

import asyncio
from enum import Enum
from typing import Optional, Dict, TYPE_CHECKING
from dataclasses import dataclass

try:
    from tree_sitter import Language, Parser, Tree
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    Language = None
    Parser = None
    Tree = None

from ..base.common.languages.language_service import ILanguageServiceProvider
from ..base.common.languages.languages import LanguageIdentifier

if TYPE_CHECKING:
    from ..code_context.base.language_profile import LanguageProfile
    from ..code_search.scope_graph.scope_graph import ScopeGraph


class TreeSitterFileError(Enum):
    """Errors that can occur when creating TreeSitterFile"""
    UNSUPPORTED_LANGUAGE = "unsupported_language"
    PARSE_TIMEOUT = "parse_timeout"
    LANGUAGE_MISMATCH = "language_mismatch"
    QUERY_ERROR = "query_error"
    FILE_TOO_LARGE = "file_too_large"


def is_larger_than_500kb(content: str) -> bool:
    """Check if content is larger than 500KB"""
    return len(content.encode('utf-8')) > 500 * 1024


class TreeSitterFile:
    """Tree-sitter file representation with parsing and analysis capabilities"""

    def __init__(
        self,
        source_code: str,
        tree: 'Tree',
        language_profile: 'LanguageProfile',
        parser: Optional['Parser'],
        ts_language: 'Language',
        file_path: str = ''
    ):
        self.source_code = source_code
        self.tree = tree
        self.language_profile = language_profile
        self.parser = parser
        self.ts_language = ts_language
        self.file_path = file_path

        # Cache for scope graph
        self._scope_graph_cache: Optional['ScopeGraph'] = None

    # One second timeout in microseconds
    ONE_SECOND_MICROS = 1_000_000

    @classmethod
    async def create(
        cls,
        source: str,
        lang_id: str,
        language_service: ILanguageServiceProvider,
        file_path: str = ''
    ) -> 'TreeSitterFile':
        """
        Create a new TreeSitterFile instance

        Args:
            source: Source code content
            lang_id: Language identifier
            language_service: Language service provider
            file_path: Optional file path

        Returns:
            TreeSitterFile instance

        Raises:
            TreeSitterFileError: If creation fails
        """
        if not HAS_TREE_SITTER:
            raise ImportError("tree-sitter is required")

        # Check file size limit (500KB)
        if is_larger_than_500kb(source):
            raise ValueError(TreeSitterFileError.FILE_TOO_LARGE.value)

        # Get language profile
        from ..code_context.base.language_profile_util import LanguageProfileUtil
        ts_config = LanguageProfileUtil.from_id(lang_id)
        if ts_config is None:
            raise ValueError(TreeSitterFileError.UNSUPPORTED_LANGUAGE.value)

        # Initialize language service
        await language_service.ready()

        # Get parser and language
        parser = await language_service.get_parser(lang_id)
        if parser is None:
            raise ValueError(TreeSitterFileError.LANGUAGE_MISMATCH.value)

        language = await language_service.get_language(lang_id)
        if language is None:
            raise ValueError(TreeSitterFileError.LANGUAGE_MISMATCH.value)

        try:
            # Set timeout to prevent long parsing times
            # Note: Python tree-sitter doesn't have setTimeoutMicros,
            # so we'll implement a timeout using asyncio

            # Parse the source code
            if isinstance(source, str):
                source_bytes = source.encode('utf-8')
            else:
                source_bytes = source

            # Parse with timeout
            tree = await asyncio.wait_for(
                asyncio.get_event_loop().run_in_executor(
                    None, parser.parse, source_bytes
                ),
                timeout=1.0  # 1 second timeout
            )

            if tree is None:
                raise ValueError(TreeSitterFileError.PARSE_TIMEOUT.value)

            return cls(source, tree, ts_config, parser, language, file_path)

        except asyncio.TimeoutError:
            raise ValueError(TreeSitterFileError.PARSE_TIMEOUT.value)
        except Exception as e:
            raise ValueError(TreeSitterFileError.LANGUAGE_MISMATCH.value) from e

    @classmethod
    async def from_parser(
        cls,
        parser: 'Parser',
        language_service: ILanguageServiceProvider,
        lang_id: LanguageIdentifier,
        code: str
    ) -> 'TreeSitterFile':
        """
        Create TreeSitterFile from existing parser

        Args:
            parser: Tree-sitter parser
            language_service: Language service provider
            lang_id: Language identifier
            code: Source code

        Returns:
            TreeSitterFile instance
        """
        from ..code_context.base.language_profile_util import LanguageProfileUtil

        lang_config = LanguageProfileUtil.from_id(lang_id)
        if lang_config is None:
            raise ValueError(f"Unsupported language: {lang_id}")

        language = await language_service.get_language(lang_id)
        if language is None:
            raise ValueError(f"Cannot get language for: {lang_id}")

        # Parse the code
        if isinstance(code, str):
            code_bytes = code.encode('utf-8')
        else:
            code_bytes = code

        tree = parser.parse(code_bytes)
        return cls(code, tree, lang_config, parser, language, '')

    def update(self, tree: 'Tree', source_code: str) -> None:
        """Update the tree and source code"""
        self.tree = tree
        self.source_code = source_code
        # Clear scope graph cache
        self._scope_graph_cache = None

    async def scope_graph(self) -> 'ScopeGraph':
        """
        Generate a scope graph for the current file

        A scope graph is a representation of the scopes and their relationships in a program.
        This method uses a ScopeBuilder to build the scope graph.

        Returns:
            ScopeGraph object representing the scopes in the file
        """
        if self._scope_graph_cache is not None:
            return self._scope_graph_cache

        from ..code_search.scope_graph.scope_builder import ScopeBuilder

        # Get scope query from language profile
        query = self.language_profile.scope_query.query(self.ts_language)
        root_node = self.tree.root_node

        # Build scope graph
        scope_builder = ScopeBuilder(
            query, root_node, self.source_code, self.language_profile
        )
        scope_graph = await scope_builder.build()

        # Cache the result
        self._scope_graph_cache = scope_graph
        return scope_graph

    def is_test_file(self) -> bool:
        """Check if this is a test file based on the language profile"""
        return self.language_profile.is_test_file(self.file_path)

    def get_root_node(self) -> Optional['Node']:
        """Get the root node of the syntax tree"""
        if self.tree:
            return self.tree.root_node
        return None

    def get_source_lines(self) -> list[str]:
        """Get source code split into lines"""
        return self.source_code.splitlines()

    def get_text_at_range(self, start_line: int, start_col: int,
                         end_line: int, end_col: int) -> str:
        """Get text content at specified range"""
        lines = self.get_source_lines()

        if start_line == end_line:
            # Single line
            if start_line < len(lines):
                line = lines[start_line]
                return line[start_col:end_col]
        else:
            # Multiple lines
            result_lines = []
            for i in range(start_line, min(end_line + 1, len(lines))):
                line = lines[i]
                if i == start_line:
                    result_lines.append(line[start_col:])
                elif i == end_line:
                    result_lines.append(line[:end_col])
                else:
                    result_lines.append(line)
            return '\n'.join(result_lines)

        return ""