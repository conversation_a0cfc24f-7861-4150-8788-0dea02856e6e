"""
Text range representation for AST nodes
"""

from dataclasses import dataclass
from typing import Optional

try:
    from tree_sitter import Node
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    Node = None


@dataclass
class TextInRange:
    """Represents a text range with start and end positions"""

    start_line: int
    start_column: int
    end_line: int
    end_column: int
    text: str = ""

    @classmethod
    def from_node(cls, node: 'Node') -> 'TextInRange':
        """Create TextInRange from a tree-sitter node"""
        if not HAS_TREE_SITTER or node is None:
            return cls(0, 0, 0, 0, "")

        start_point = node.start_point
        end_point = node.end_point

        return cls(
            start_line=start_point[0],
            start_column=start_point[1],
            end_line=end_point[0],
            end_column=end_point[1],
            text=node.text.decode('utf-8') if hasattr(node, 'text') else ""
        )

    @classmethod
    def from_positions(cls, start_line: int, start_column: int,
                      end_line: int, end_column: int, text: str = "") -> 'TextInRange':
        """Create TextInRange from explicit positions"""
        return cls(start_line, start_column, end_line, end_column, text)

    def contains_position(self, line: int, column: int) -> bool:
        """Check if a position is within this range"""
        if line < self.start_line or line > self.end_line:
            return False

        if line == self.start_line and column < self.start_column:
            return False

        if line == self.end_line and column > self.end_column:
            return False

        return True

    def overlaps_with(self, other: 'TextInRange') -> bool:
        """Check if this range overlaps with another range"""
        return not (
            self.end_line < other.start_line or
            other.end_line < self.start_line or
            (self.end_line == other.start_line and self.end_column < other.start_column) or
            (other.end_line == self.start_line and other.end_column < self.start_column)
        )

    def __str__(self) -> str:
        return f"TextInRange({self.start_line}:{self.start_column}-{self.end_line}:{self.end_column})"

    def __repr__(self) -> str:
        return self.__str__()