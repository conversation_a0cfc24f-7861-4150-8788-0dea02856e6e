"""
Named element builder for extracting code elements from AST
"""

from typing import List, Optional, TYPE_CHECKING

try:
    from tree_sitter import Language, Parser, Tree
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    Language = None
    Parser = None
    Tree = None

from .named_element import NamedElement
from .text_in_range import TextInRange
from .tree_sitter_util import TreeSitterUtil

if TYPE_CHECKING:
    from .tree_sitter_file import TreeSitterFile
    from ..code_context.base.language_profile import LanguageProfile, MemoizedQuery
    from ..codemodel.code_element_type import CodeElementType


class NamedElementBuilder:
    """
    Builder class for extracting named elements (such as variables, methods, and classes)
    from a TreeSitter file. It uses the Tree-sitter parsing library to parse the source
    code and extract the named elements, like:
    - Class
    - Method
    - Variable
    """

    def __init__(self, file: 'TreeSitterFile'):
        """
        Create a new NamedElementBuilder object.

        Args:
            file: The TreeSitter file to build named elements from.
        """
        self.lang_config: 'LanguageProfile' = file.language_profile
        self.tree: 'Tree' = file.tree
        self.language: 'Language' = file.ts_language
        self.parser: Optional['Parser'] = file.parser
        self.file = file

    def build_variable(self) -> List[NamedElement]:
        """Build variable elements (not implemented yet)"""
        raise NotImplementedError("Variable building not implemented yet")

    def build_method(self) -> List[NamedElement]:
        """Build method elements"""
        from ..codemodel.code_element_type import CodeElementType
        return self.build_block(self.lang_config.method_query, CodeElementType.METHOD)

    def build_class(self) -> List[NamedElement]:
        """Build class/structure elements"""
        from ..codemodel.code_element_type import CodeElementType
        return self.build_block(self.lang_config.class_query, CodeElementType.STRUCTURE)

    def build_function(self) -> List[NamedElement]:
        """Build function elements"""
        from ..codemodel.code_element_type import CodeElementType
        return self.build_block(self.lang_config.function_query, CodeElementType.FUNCTION)

    def build_all(self) -> List[NamedElement]:
        """Build all supported named elements"""
        elements = []

        try:
            elements.extend(self.build_class())
        except (NotImplementedError, AttributeError):
            pass

        try:
            elements.extend(self.build_method())
        except (NotImplementedError, AttributeError):
            pass

        try:
            elements.extend(self.build_function())
        except (NotImplementedError, AttributeError):
            pass

        return elements

    def build_block(self, memoized_query: 'MemoizedQuery',
                   element_type: 'CodeElementType') -> List[NamedElement]:
        """
        Search the syntax tree for matches to the given query and return
        a list of named elements.

        Args:
            memoized_query: The memoized query object to use for the search
            element_type: The type of code element that the query represents

        Returns:
            List of NamedElement objects representing the matches
        """
        if not HAS_TREE_SITTER:
            return []

        try:
            query = memoized_query.query(self.language)
            if query is None:
                return []

            root = self.tree.root_node
            matches = query.matches(root)

            if not matches:
                return []

            elements = []
            for match in matches:
                if len(match.captures) < 2:
                    continue

                # Get block and identifier nodes
                block_node = match.captures[0].node
                id_node = match.captures[1].node

                # Handle auto-select inside parent
                inside_parent = getattr(self.lang_config, 'auto_select_inside_parent', [])
                if inside_parent:
                    for node_type in inside_parent:
                        if block_node.parent and block_node.parent.type == node_type:
                            block_node = block_node.parent

                # Create named element
                block_range = TextInRange.from_node(block_node)
                identifier_range = TextInRange.from_node(id_node)
                block_content = TreeSitterUtil.get_node_text(block_node)

                named_element = NamedElement(
                    block_range=block_range,
                    identifier_range=identifier_range,
                    code_element_type=element_type,
                    block_content=block_content,
                    file=self.file
                )

                # Look for associated comments
                comment_nodes = TreeSitterUtil.previous_nodes_of_type(
                    block_node, ['block_comment', 'line_comment', 'comment']
                )
                if comment_nodes:
                    named_element.comment_range = TextInRange.from_node(comment_nodes[0])

                elements.append(named_element)

            return elements

        except Exception as e:
            # Log error but don't fail completely
            print(f"Error building blocks for {element_type}: {e}")
            return []