# Context Worker Python 安装指南

## 🐍 Python 版本要求

**Context Worker Python 要求 Python 3.10 或更高版本**

## 📋 环境检查

在安装之前，请确认你的 Python 版本：

```bash
python --version
# 或
python3 --version
```

如果版本低于 3.10，请先升级 Python。

## 🚀 安装方法

### 方法 1: 开发环境安装（推荐）

```bash
# 1. 进入 context_worker_py 目录
cd context_worker_py

# 2. 创建虚拟环境（推荐）
python -m venv venv

# 3. 激活虚拟环境
# Linux/macOS:
source venv/bin/activate
# Windows:
# venv\Scripts\activate

# 4. 升级 pip
pip install --upgrade pip

# 5. 安装开发环境
pip install -e ".[dev,cli]"
```

### 方法 2: 基础安装

```bash
cd context_worker_py
pip install -e .
```

### 方法 3: 仅安装核心依赖

```bash
cd context_worker_py
pip install -e . --no-deps
pip install tree-sitter tree-sitter-languages pyyaml click
```

## 🔧 可选依赖安装

### CLI 工具支持
```bash
pip install -e ".[cli]"
```

### 开发工具支持
```bash
pip install -e ".[dev]"
```

### 文档生成支持
```bash
pip install -e ".[docs]"
```

### 全部安装
```bash
pip install -e ".[dev,cli,docs]"
```

## ✅ 安装验证

### 1. 运行包测试
```bash
cd context_worker_py
python test_package.py
```

### 2. 运行示例
```bash
python run_example.py
```

### 3. 测试命令行工具
```bash
context-worker --help
context-worker-finder --help
```

### 4. 测试 Python 导入
```bash
python -c "
from context_worker_py import FileSystemScanner, CodeCollector, SymbolAnalyser
print('✅ 核心组件导入成功')

from context_worker_py.examples import ContextWorkerExample, TaskContextFinder
print('✅ 示例组件导入成功')

print('🎉 Context Worker Python 安装成功！')
"
```

## 🐛 常见问题

### 问题 1: Python 版本不兼容
```
ERROR: This package requires Python >=3.10
```

**解决方案**: 升级到 Python 3.10 或更高版本

### 问题 2: tree-sitter 安装失败
```
ERROR: Failed building wheel for tree-sitter
```

**解决方案**: 
```bash
# 安装编译工具
# Ubuntu/Debian:
sudo apt-get install build-essential

# macOS:
xcode-select --install

# 然后重新安装
pip install tree-sitter
```

### 问题 3: 权限错误
```
ERROR: Permission denied
```

**解决方案**: 使用虚拟环境或添加 `--user` 参数
```bash
pip install --user -e .
```

### 问题 4: 依赖冲突
```
ERROR: Conflicting dependencies
```

**解决方案**: 使用新的虚拟环境
```bash
python -m venv fresh_env
source fresh_env/bin/activate  # Linux/macOS
pip install -e ".[dev,cli]"
```

## 🔄 更新和卸载

### 更新
```bash
cd context_worker_py
pip install -e . --upgrade
```

### 卸载
```bash
pip uninstall context-worker-py
```

## 🌟 推荐的开发环境设置

### 1. 使用 pyenv 管理 Python 版本
```bash
# 安装 pyenv (如果还没有)
curl https://pyenv.run | bash

# 安装 Python 3.10+
pyenv install 3.10.12
pyenv local 3.10.12
```

### 2. 使用 virtualenv
```bash
pip install virtualenv
virtualenv -p python3.10 context_worker_env
source context_worker_env/bin/activate
```

### 3. 使用 conda
```bash
conda create -n context_worker python=3.10
conda activate context_worker
cd context_worker_py
pip install -e ".[dev,cli]"
```

## 📊 系统要求

- **Python**: 3.10+
- **操作系统**: Linux, macOS, Windows
- **内存**: 建议 4GB+
- **磁盘空间**: 500MB+

## 🎯 快速验证脚本

创建并运行以下脚本来验证安装：

```python
#!/usr/bin/env python3
"""快速验证 Context Worker Python 安装"""

import sys
import subprocess

def check_python_version():
    """检查 Python 版本"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 10:
        print(f"✅ Python 版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python 版本过低: {version.major}.{version.minor}.{version.micro}")
        print("需要 Python 3.10 或更高版本")
        return False

def check_installation():
    """检查安装"""
    try:
        import context_worker_py
        print("✅ context_worker_py 导入成功")
        
        from context_worker_py.examples import ContextWorkerExample
        print("✅ 示例模块导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def check_cli_tools():
    """检查命令行工具"""
    try:
        result = subprocess.run(['context-worker', '--help'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ context-worker 命令可用")
        else:
            print("❌ context-worker 命令不可用")
    except FileNotFoundError:
        print("❌ context-worker 命令未找到")

if __name__ == "__main__":
    print("🔍 Context Worker Python 安装验证")
    print("=" * 40)
    
    if check_python_version() and check_installation():
        check_cli_tools()
        print("\n🎉 安装验证完成！")
    else:
        print("\n❌ 安装验证失败，请检查安装步骤")
```

保存为 `verify_install.py` 并运行：
```bash
python verify_install.py
```
