#!/usr/bin/env python3
"""
Context Worker Python 运行示例

这个脚本演示如何运行 context_worker_py 的各种功能。
"""

import asyncio
import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))

# 直接导入，避免循环导入
from examples.context_worker_example import ContextWorkerExample
from context_worker_py.task_context_finder import TaskContextFinder


async def run_basic_example():
    """运行基础示例"""
    print("🚀 运行基础 Context Worker 示例")
    print("=" * 50)
    
    # 使用当前目录作为示例项目
    project_path = "."
    task_description = "文件扫描和代码分析功能"
    
    try:
        # 创建分析器实例
        analyzer = ContextWorkerExample(project_path)
        
        # 运行完整分析
        result = await analyzer.run_complete_analysis(task_description)
        
        print("\n✅ 基础示例运行完成")
        return result
        
    except Exception as e:
        print(f"❌ 基础示例运行失败: {e}")
        return None


async def run_task_finder_example():
    """运行任务查找器示例"""
    print("\n🎯 运行任务上下文查找器示例")
    print("=" * 50)
    
    project_path = "."
    task_description = "代码分析和符号提取"
    
    try:
        # 创建查找器
        finder = TaskContextFinder(project_path)
        
        # 查找任务上下文
        context = await finder.find_task_context(task_description)
        
        # 打印结果
        finder.print_results(context)
        
        print("\n✅ 任务查找器示例运行完成")
        return context
        
    except Exception as e:
        print(f"❌ 任务查找器示例运行失败: {e}")
        return None


async def main():
    """主函数"""
    print("🌟 Context Worker Python 示例运行器")
    print("=" * 60)
    
    # 检查是否有命令行参数
    if len(sys.argv) > 1:
        example_type = sys.argv[1].lower()
    else:
        example_type = "all"
    
    if example_type in ["basic", "all"]:
        await run_basic_example()
    
    if example_type in ["finder", "all"]:
        await run_task_finder_example()
    
    if example_type not in ["basic", "finder", "all"]:
        print("可用的示例类型:")
        print("  basic  - 运行基础 Context Worker 示例")
        print("  finder - 运行任务上下文查找器示例")
        print("  all    - 运行所有示例 (默认)")
        print("\n使用方法: python run_example.py [示例类型]")
    
    print("\n🎉 示例运行完成!")


if __name__ == "__main__":
    asyncio.run(main())
