#!/usr/bin/env python3
"""
Context Worker Python 自动安装脚本

这个脚本会检查环境并自动安装 Context Worker Python。
"""

import sys
import subprocess
import os
from pathlib import Path


def check_python_version():
    """检查 Python 版本"""
    version = sys.version_info
    print(f"🐍 当前 Python 版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 10:
        print("✅ Python 版本符合要求 (>=3.10)")
        return True
    else:
        print("❌ Python 版本过低，需要 Python 3.10 或更高版本")
        print("\n请升级 Python 后重试:")
        print("  - 使用 pyenv: pyenv install 3.10.12 && pyenv local 3.10.12")
        print("  - 使用 conda: conda install python=3.10")
        print("  - 或从官网下载: https://www.python.org/downloads/")
        return False


def check_pip():
    """检查 pip 是否可用"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip 可用")
        return True
    except subprocess.CalledProcessError:
        print("❌ pip 不可用")
        return False


def upgrade_pip():
    """升级 pip"""
    print("🔄 升级 pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True)
        print("✅ pip 升级成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ pip 升级失败: {e}")
        return False


def install_package(install_type="dev"):
    """安装包"""
    print(f"📦 安装 Context Worker Python ({install_type})...")
    
    install_commands = {
        "basic": [sys.executable, "-m", "pip", "install", "-e", "."],
        "cli": [sys.executable, "-m", "pip", "install", "-e", ".[cli]"],
        "dev": [sys.executable, "-m", "pip", "install", "-e", ".[dev,cli]"],
        "full": [sys.executable, "-m", "pip", "install", "-e", ".[dev,cli,docs]"]
    }
    
    cmd = install_commands.get(install_type, install_commands["dev"])
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False


def verify_installation():
    """验证安装"""
    print("🔍 验证安装...")
    
    try:
        # 测试导入
        import context_worker_py
        print("✅ 核心模块导入成功")
        
        from context_worker_py.examples import ContextWorkerExample
        print("✅ 示例模块导入成功")
        
        # 测试命令行工具
        try:
            result = subprocess.run(['context-worker', '--help'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ context-worker 命令可用")
            else:
                print("⚠️  context-worker 命令可能有问题")
        except (FileNotFoundError, subprocess.TimeoutExpired):
            print("⚠️  context-worker 命令未找到或超时")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False


def run_test():
    """运行测试"""
    print("🧪 运行包测试...")
    
    test_file = Path("test_package.py")
    if test_file.exists():
        try:
            subprocess.run([sys.executable, "test_package.py"], check=True)
            print("✅ 包测试通过")
            return True
        except subprocess.CalledProcessError:
            print("❌ 包测试失败")
            return False
    else:
        print("⚠️  测试文件不存在，跳过测试")
        return True


def main():
    """主安装流程"""
    print("🚀 Context Worker Python 自动安装程序")
    print("=" * 50)
    
    # 检查当前目录
    if not Path("pyproject.toml").exists():
        print("❌ 请在 context_worker_py 目录下运行此脚本")
        sys.exit(1)
    
    # 检查 Python 版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查 pip
    if not check_pip():
        print("请先安装 pip")
        sys.exit(1)
    
    # 升级 pip
    upgrade_pip()
    
    # 选择安装类型
    print("\n📋 选择安装类型:")
    print("1. 基础安装 (basic)")
    print("2. 包含CLI工具 (cli)")
    print("3. 开发环境 (dev) - 推荐")
    print("4. 完整安装 (full)")
    
    choice = input("\n请选择 (1-4, 默认3): ").strip()
    
    install_types = {
        "1": "basic",
        "2": "cli", 
        "3": "dev",
        "4": "full"
    }
    
    install_type = install_types.get(choice, "dev")
    
    # 安装
    if not install_package(install_type):
        print("\n❌ 安装失败")
        sys.exit(1)
    
    # 验证
    if not verify_installation():
        print("\n❌ 验证失败")
        sys.exit(1)
    
    # 运行测试
    run_test()
    
    print("\n" + "=" * 50)
    print("🎉 Context Worker Python 安装完成！")
    print("\n📚 接下来你可以:")
    print("  • 运行示例: python run_example.py")
    print("  • 查看文档: cat docs/quick_start_guide.md")
    print("  • 使用CLI: context-worker --help")
    print("  • 查找上下文: context-worker-finder . '你的任务描述'")


if __name__ == "__main__":
    main()
