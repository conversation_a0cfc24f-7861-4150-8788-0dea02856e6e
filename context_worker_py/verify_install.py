#!/usr/bin/env python3
"""
Context Worker Python 安装验证脚本

快速验证 Context Worker Python 是否正确安装。
"""

import sys
import subprocess
import importlib.util


def check_python_version():
    """检查 Python 版本"""
    version = sys.version_info
    print(f"🐍 Python 版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 10:
        print("✅ Python 版本符合要求 (>=3.10)")
        return True
    else:
        print("❌ Python 版本过低，需要 Python 3.10 或更高版本")
        return False


def check_package_installed(package_name):
    """检查包是否已安装"""
    spec = importlib.util.find_spec(package_name)
    return spec is not None


def check_core_dependencies():
    """检查核心依赖"""
    print("\n📦 检查核心依赖:")
    
    core_deps = [
        "tree_sitter",
        "yaml", 
        "click",
        "requests",
        "bs4",
        "markdown",
        "git",
        "chardet",
        "pathspec"
    ]
    
    missing_deps = []
    
    for dep in core_deps:
        if check_package_installed(dep):
            print(f"  ✅ {dep}")
        else:
            print(f"  ❌ {dep}")
            missing_deps.append(dep)
    
    return len(missing_deps) == 0, missing_deps


def check_context_worker_import():
    """检查 Context Worker 导入"""
    print("\n🔍 检查 Context Worker 导入:")
    
    try:
        import context_worker_py
        print("  ✅ context_worker_py 主模块")
        
        from context_worker_py import FileSystemScanner, CodeCollector, SymbolAnalyser
        print("  ✅ 核心组件")
        
        from context_worker_py import AppConfig, AnalysisTypes
        print("  ✅ 配置类型")
        
        from context_worker_py.examples import ContextWorkerExample, TaskContextFinder
        print("  ✅ 示例模块")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ 导入失败: {e}")
        return False


def check_cli_tools():
    """检查命令行工具"""
    print("\n🛠️  检查命令行工具:")
    
    tools = ["context-worker", "context-worker-finder"]
    available_tools = []
    
    for tool in tools:
        try:
            result = subprocess.run([tool, "--help"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"  ✅ {tool}")
                available_tools.append(tool)
            else:
                print(f"  ❌ {tool} (返回码: {result.returncode})")
        except FileNotFoundError:
            print(f"  ❌ {tool} (未找到)")
        except subprocess.TimeoutExpired:
            print(f"  ⚠️  {tool} (超时)")
    
    return len(available_tools) > 0


def run_basic_functionality_test():
    """运行基础功能测试"""
    print("\n🧪 运行基础功能测试:")
    
    try:
        from context_worker_py import FileSystemScanner, CodeCollector
        
        # 测试文件扫描器
        scanner = FileSystemScanner()
        print("  ✅ FileSystemScanner 创建成功")
        
        # 测试代码收集器
        collector = CodeCollector(".")
        print("  ✅ CodeCollector 创建成功")
        
        # 测试配置
        from context_worker_py import AppConfig, AnalysisTypes
        config = AppConfig(
            dir_path=".",
            analysis_types=AnalysisTypes(interface=True, api=True, symbol=True),
            upload=False,
            base_url="http://localhost:3000",
            output_dir="./test_output",
            non_interactive=True,
            context_type="interface"
        )
        print("  ✅ AppConfig 创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 功能测试失败: {e}")
        return False


def main():
    """主验证流程"""
    print("🔍 Context Worker Python 安装验证")
    print("=" * 50)
    
    # 检查 Python 版本
    if not check_python_version():
        print("\n❌ Python 版本不符合要求")
        return False
    
    # 检查核心依赖
    deps_ok, missing_deps = check_core_dependencies()
    if not deps_ok:
        print(f"\n❌ 缺少依赖: {missing_deps}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    # 检查 Context Worker 导入
    if not check_context_worker_import():
        print("\n❌ Context Worker 导入失败")
        print("请运行: pip install -e .")
        return False
    
    # 检查命令行工具
    cli_ok = check_cli_tools()
    if not cli_ok:
        print("\n⚠️  命令行工具不可用")
        print("请运行: pip install -e .[cli]")
    
    # 运行基础功能测试
    if not run_basic_functionality_test():
        print("\n❌ 基础功能测试失败")
        return False
    
    # 总结
    print("\n" + "=" * 50)
    if cli_ok:
        print("🎉 Context Worker Python 安装验证完全通过！")
        print("\n📚 接下来你可以:")
        print("  • 运行示例: python run_example.py")
        print("  • 使用CLI: context-worker --help")
        print("  • 查找上下文: context-worker-finder . '任务描述'")
    else:
        print("✅ Context Worker Python 核心功能正常")
        print("⚠️  命令行工具需要额外安装")
        print("\n安装CLI工具: pip install -e .[cli]")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
