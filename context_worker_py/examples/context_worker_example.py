#!/usr/bin/env python3
"""
Context Worker Python 使用示例

这个示例展示了如何使用 context_worker_py 来分析代码仓库，
查找与开发任务相关的上下文代码。
"""

import asyncio
import json
import os
from typing import List, Dict, Any

# 导入 context_worker_py 的核心组件
from context_worker_py import (
    # 配置相关
    AppConfig,
    AnalysisTypes,
    
    # 文件扫描和代码收集
    FileSystemScanner,
    CodeCollector,
    
    # 分析器
    SymbolAnalyser,
    InterfaceAnalyzer,
    
    # 代码搜索和上下文
    ScopeGraph,
    ScopeBuilder,
    
    # 文档分析
    MarkdownAnalyser,
    
    # 主入口
    run
)


class ContextWorkerExample:
    """Context Worker 使用示例类"""
    
    def __init__(self, project_path: str):
        """
        初始化示例
        
        Args:
            project_path: 要分析的项目路径
        """
        self.project_path = os.path.abspath(project_path)
        self.scanner = FileSystemScanner()
        self.collector = CodeCollector(self.project_path)
        self.symbol_analyser = SymbolAnalyser()
        self.interface_analyzer = InterfaceAnalyzer()
        self.markdown_analyser = MarkdownAnalyser()
        
    async def analyze_project_structure(self) -> Dict[str, Any]:
        """
        分析项目结构
        
        Returns:
            项目结构分析结果
        """
        print(f"🔍 开始扫描项目: {self.project_path}")
        
        # 1. 扫描文件系统
        files = await self.scanner.scan_directory(self.project_path)
        print(f"📁 发现 {len(files)} 个文件")
        
        # 2. 分类文件
        code_files = []
        doc_files = []
        
        for file_path in files:
            if file_path.endswith(('.py', '.js', '.ts', '.java', '.cpp', '.c', '.go', '.rs')):
                code_files.append(file_path)
            elif file_path.endswith(('.md', '.rst', '.txt')):
                doc_files.append(file_path)
        
        print(f"💻 代码文件: {len(code_files)} 个")
        print(f"📖 文档文件: {len(doc_files)} 个")
        
        return {
            'total_files': len(files),
            'code_files': code_files,
            'doc_files': doc_files,
            'all_files': files
        }
    
    async def analyze_symbols(self, code_files: List[str]) -> Dict[str, Any]:
        """
        分析代码符号
        
        Args:
            code_files: 代码文件列表
            
        Returns:
            符号分析结果
        """
        print("🔬 开始符号分析...")
        
        # 收集代码文件到 collector
        for file_path in code_files[:10]:  # 限制文件数量以避免过长处理
            try:
                # 这里需要实际的代码解析逻辑
                # 目前只是示例，实际使用时需要完整的解析器
                print(f"  📄 分析文件: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"  ❌ 分析文件失败 {file_path}: {e}")
        
        # 执行符号分析
        try:
            symbol_result = await self.symbol_analyser.analyze(self.collector)
            print(f"✅ 符号分析完成，发现 {len(symbol_result.symbols)} 个符号")
            return {
                'symbol_count': len(symbol_result.symbols),
                'file_symbols': symbol_result.file_symbols,
                'stats': symbol_result.stats
            }
        except Exception as e:
            print(f"❌ 符号分析失败: {e}")
            return {'error': str(e)}
    
    async def find_related_context(self, task_description: str, symbol_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        根据开发任务描述查找相关的上下文代码
        
        Args:
            task_description: 开发任务描述
            symbol_results: 符号分析结果
            
        Returns:
            相关上下文代码列表
        """
        print(f"🎯 查找与任务相关的上下文: {task_description}")
        
        # 简单的关键词匹配示例
        # 实际应用中可以使用更复杂的语义匹配
        keywords = task_description.lower().split()
        related_contexts = []
        
        if 'file_symbols' in symbol_results:
            for file_path, file_symbols in symbol_results['file_symbols'].items():
                for symbol in file_symbols.symbols:
                    # 检查符号名称是否包含任务关键词
                    symbol_name_lower = symbol.name.lower()
                    if any(keyword in symbol_name_lower for keyword in keywords):
                        related_contexts.append({
                            'file_path': file_path,
                            'symbol_name': symbol.name,
                            'symbol_kind': symbol.kind,
                            'qualified_name': symbol.qualified_name,
                            'position': {
                                'start': {'line': symbol.position.start.row, 'column': symbol.position.start.column},
                                'end': {'line': symbol.position.end.row, 'column': symbol.position.end.column}
                            },
                            'relevance_reason': f"符号名称包含关键词: {[k for k in keywords if k in symbol_name_lower]}"
                        })
        
        print(f"🔍 找到 {len(related_contexts)} 个相关上下文")
        return related_contexts
    
    async def analyze_with_config(self) -> None:
        """使用配置文件进行完整分析"""
        print("⚙️ 使用配置进行完整分析...")
        
        config = AppConfig(
            dir_path=self.project_path,
            analysis_types=AnalysisTypes(
                interface=True,
                api=True,
                symbol=True
            ),
            upload=False,
            base_url="http://localhost:3000",
            output_dir="./analysis_output",
            non_interactive=True,
            context_type="interface",
            output_json_file="analysis_result.json"
        )
        
        try:
            await run(config)
            print("✅ 配置分析完成")
        except Exception as e:
            print(f"❌ 配置分析失败: {e}")
    
    async def run_complete_analysis(self, task_description: str = "用户认证功能") -> Dict[str, Any]:
        """
        运行完整的代码上下文分析
        
        Args:
            task_description: 开发任务描述
            
        Returns:
            完整的分析结果
        """
        print("🚀 开始完整的代码上下文分析")
        print(f"📋 任务描述: {task_description}")
        print("=" * 50)
        
        # 1. 分析项目结构
        structure_result = await self.analyze_project_structure()
        
        # 2. 分析符号
        symbol_result = await self.analyze_symbols(structure_result['code_files'])
        
        # 3. 查找相关上下文
        related_contexts = await self.find_related_context(task_description, symbol_result)
        
        # 4. 汇总结果
        final_result = {
            'task_description': task_description,
            'project_path': self.project_path,
            'structure_analysis': structure_result,
            'symbol_analysis': symbol_result,
            'related_contexts': related_contexts,
            'summary': {
                'total_files': structure_result['total_files'],
                'code_files_count': len(structure_result['code_files']),
                'related_contexts_count': len(related_contexts)
            }
        }
        
        print("=" * 50)
        print("📊 分析结果摘要:")
        print(f"  📁 总文件数: {final_result['summary']['total_files']}")
        print(f"  💻 代码文件数: {final_result['summary']['code_files_count']}")
        print(f"  🎯 相关上下文数: {final_result['summary']['related_contexts_count']}")
        
        if related_contexts:
            print("\n🔍 相关上下文详情:")
            for i, context in enumerate(related_contexts[:5], 1):  # 只显示前5个
                print(f"  {i}. {context['symbol_name']} ({context['file_path']})")
                print(f"     原因: {context['relevance_reason']}")
        
        return final_result


async def main():
    """主函数示例"""
    # 使用当前目录作为示例项目
    project_path = "."
    task_description = "文件扫描和代码分析功能"
    
    # 创建分析器实例
    analyzer = ContextWorkerExample(project_path)
    
    # 运行完整分析
    result = await analyzer.run_complete_analysis(task_description)
    
    # 保存结果到文件
    output_file = "context_analysis_result.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n💾 分析结果已保存到: {output_file}")
    
    # 也可以使用配置文件方式
    print("\n" + "=" * 50)
    await analyzer.analyze_with_config()


if __name__ == "__main__":
    asyncio.run(main())
