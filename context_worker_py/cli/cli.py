"""
Command line interface for context worker
"""

import os
import sys
import argparse
from pathlib import Path
from typing import Optional

from ..type_definitions.app_config import AppConfig, AnalysisTypes, DEFAULT_CONFIG


class CommandLineParser:
    """Command line argument parser"""

    def __init__(self):
        self.parser = self._create_parser()

    def _create_parser(self) -> argparse.ArgumentParser:
        """Create the argument parser"""
        parser = argparse.ArgumentParser(
            prog='context-worker',
            description='AutoDev Context Worker - Code analysis and context building tool',
            formatter_class=argparse.RawDescriptionHelpFormatter
        )

        parser.add_argument(
            '--version',
            action='version',
            version='%(prog)s 1.0.0'
        )

        parser.add_argument(
            '-p', '--path',
            dest='dir_path',
            default=DEFAULT_CONFIG.dir_path,
            help='Directory path to scan (default: current directory)'
        )

        parser.add_argument(
            '-u', '--upload',
            action='store_true',
            default=DEFAULT_CONFIG.upload,
            help='Upload analysis results to server'
        )

        parser.add_argument(
            '--server-url',
            dest='base_url',
            default=DEFAULT_CONFIG.base_url,
            help='Server URL for uploading results'
        )

        parser.add_argument(
            '-o', '--output-dir',
            dest='output_dir',
            default=DEFAULT_CONFIG.output_dir,
            help='Output directory for learning materials'
        )

        parser.add_argument(
            '-n', '--non-interactive',
            dest='non_interactive',
            action='store_true',
            default=DEFAULT_CONFIG.non_interactive,
            help='Run in non-interactive mode'
        )

        parser.add_argument(
            '--output-file',
            dest='output_json_file',
            default=DEFAULT_CONFIG.output_json_file,
            help='JSON output file name'
        )

        parser.add_argument(
            '--interface',
            action='store_true',
            help='Process interface context only'
        )

        parser.add_argument(
            '--api',
            action='store_true',
            default=True,
            help='Process API context (default: true)'
        )

        parser.add_argument(
            '--project-id',
            dest='project_id',
            help='Project ID for organization'
        )

        # Analysis type options
        parser.add_argument(
            '--run-interface',
            action='store_true',
            default=True,
            help='Run interface analysis (default: true)'
        )

        parser.add_argument(
            '--run-api',
            action='store_true',
            default=True,
            help='Run API analysis (default: true)'
        )

        parser.add_argument(
            '--run-symbol',
            action='store_true',
            default=True,
            help='Run symbol analysis (default: true)'
        )

        parser.add_argument(
            '--skip-interface',
            action='store_true',
            help='Skip interface analysis'
        )

        parser.add_argument(
            '--skip-api',
            action='store_true',
            help='Skip API analysis'
        )

        parser.add_argument(
            '--skip-symbol',
            action='store_true',
            help='Skip symbol analysis'
        )

        return parser

    def parse(self, args: Optional[list] = None) -> AppConfig:
        """Parse command line arguments and return AppConfig"""
        parsed_args = self.parser.parse_args(args)

        # Determine context type
        context_type = 'interface' if parsed_args.interface else 'api'

        # Resolve directory path
        dir_path = parsed_args.dir_path
        if not os.path.isabs(dir_path):
            dir_path = os.path.abspath(dir_path)

        # Determine analysis types
        analysis_types = AnalysisTypes(
            interface=parsed_args.run_interface and not parsed_args.skip_interface,
            api=parsed_args.run_api and not parsed_args.skip_api,
            symbol=parsed_args.run_symbol and not parsed_args.skip_symbol
        )

        return AppConfig(
            dir_path=dir_path,
            upload=parsed_args.upload,
            base_url=parsed_args.base_url,
            output_dir=parsed_args.output_dir,
            non_interactive=parsed_args.non_interactive,
            context_type=context_type,
            output_json_file=parsed_args.output_json_file,
            project_id=parsed_args.project_id,
            analysis_types=analysis_types
        )


class UserInputHandler:
    """Interactive user input handler"""

    def __init__(self):
        self._has_inquirer = self._check_inquirer()

    def _check_inquirer(self) -> bool:
        """Check if inquirer is available"""
        try:
            import inquirer
            return True
        except ImportError:
            return False

    async def get_app_config(self, current_config: AppConfig) -> AppConfig:
        """Get application configuration through interactive prompts"""
        if not self._has_inquirer:
            print("Interactive mode requires 'inquirer' package. Install with: pip install inquirer")
            print("Using current configuration...")
            return current_config

        import inquirer

        questions = [
            inquirer.Text(
                'dir_path',
                message='请输入要扫描的目录路径',
                default=current_config.dir_path,
                validate=self._validate_directory
            ),
            inquirer.Confirm(
                'upload',
                message='是否要上传分析结果到服务器?',
                default=current_config.upload
            ),
            inquirer.Text(
                'base_url',
                message='请输入服务器地址',
                default=current_config.base_url,
                ignore=lambda answers: not answers.get('upload', False)
            ),
            inquirer.Text(
                'output_dir',
                message='请输入分析结果输出目录',
                default=current_config.output_dir
            ),
            inquirer.Text(
                'output_json_file',
                message='请输入JSON结果输出文件名',
                default=current_config.output_json_file
            ),
            inquirer.Text(
                'project_id',
                message='请输入项目 ID (可选)',
                default=current_config.project_id or ''
            ),
            inquirer.Checkbox(
                'analysis_types',
                message='请选择要运行的分析类型',
                choices=[
                    ('接口分析', 'interface'),
                    ('API分析', 'api'),
                    ('关键代码标识', 'symbol')
                ],
                default=self._get_default_analysis_types(current_config.analysis_types)
            )
        ]

        answers = inquirer.prompt(questions)
        if not answers:
            return current_config

        # Resolve directory path
        dir_path = answers['dir_path']
        if not os.path.isabs(dir_path):
            dir_path = os.path.abspath(dir_path)

        # Convert analysis types
        selected_types = answers.get('analysis_types', [])
        analysis_types = AnalysisTypes(
            interface='interface' in selected_types,
            api='api' in selected_types,
            symbol='symbol' in selected_types
        )

        # If no types selected, enable all
        if not any([analysis_types['interface'], analysis_types['api'], analysis_types['symbol']]):
            analysis_types = AnalysisTypes(interface=True, api=True, symbol=True)

        return AppConfig(
            dir_path=dir_path,
            upload=answers['upload'],
            base_url=answers.get('base_url', current_config.base_url),
            output_dir=answers['output_dir'],
            non_interactive=current_config.non_interactive,
            context_type=current_config.context_type,
            output_json_file=answers['output_json_file'],
            project_id=answers['project_id'] or None,
            analysis_types=analysis_types
        )

    def _validate_directory(self, _, current: str) -> bool:
        """Validate directory path"""
        full_path = os.path.abspath(current) if not os.path.isabs(current) else current
        if os.path.exists(full_path) and os.path.isdir(full_path):
            return True
        return "请输入有效的目录路径"

    def _get_default_analysis_types(self, analysis_types: AnalysisTypes) -> list:
        """Get default analysis types for checkbox"""
        defaults = []
        if analysis_types['interface']:
            defaults.append('interface')
        if analysis_types['api']:
            defaults.append('api')
        if analysis_types['symbol']:
            defaults.append('symbol')
        return defaults