# Context Worker Python

Python port of the TypeScript @autodev/context-worker library.

## Overview

This package provides code analysis and context extraction functionality for various programming languages. It's designed to help developers understand and analyze codebases by extracting symbols, interfaces, and other contextual information.

## Features

- **Code Analysis**: Symbol extraction and interface analysis
- **AST Processing**: Tree-sitter based parsing for multiple languages
- **Language Services**: Pluggable language service providers
- **File System Scanning**: Efficient code collection and scanning
- **Context Providers**: Language-specific context extraction

## Installation

```bash
pip install context-worker-py
```

## Usage

### Basic Usage

```python
from context_worker_py import CodeCollector, SymbolAnalyser

# Initialize code collector
collector = CodeCollector("/path/to/your/project")

# Analyze symbols
analyzer = SymbolAnalyser()
results = analyzer.analyze(collector.collect_files())
```

### Using Examples

```python
from context_worker_py.examples import ContextWorkerExample, TaskContextFinder

# Complete analysis example
analyzer = ContextWorkerExample("/path/to/project")
result = await analyzer.run_complete_analysis("用户认证功能")

# Task-specific context finder
finder = TaskContextFinder("/path/to/project")
context = await finder.find_task_context("API接口开发")
```

### Command Line Tools

```bash
# Main context worker
context-worker --path /path/to/project

# Task context finder
context-worker-finder /path/to/project "用户认证功能"
```

## Package Structure

```
context_worker_py/
├── __init__.py              # Main package exports
├── README.md               # Package documentation
├── setup.py                # Package setup
├── pyproject.toml          # Build configuration
├── analyzer/               # Code analysis components
├── ast/                    # AST processing utilities
├── base/                   # Base classes and utilities
├── cli/                    # Command line interface
├── code_context/           # Language-specific context providers
├── code_search/            # Code search and scope analysis
├── codemodel/              # Code model definitions
├── document/               # Document analysis
├── examples/               # Usage examples
│   ├── context_worker_example.py
│   └── task_context_finder.py
├── docs/                   # Documentation
│   └── quick_start_guide.md
├── test/                   # Test utilities
├── types/                  # Type definitions
└── utils/                  # Utility functions
```

## Development

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest

# Format code
black context_worker_py/
isort context_worker_py/

# Type checking
mypy context_worker_py/
```

## License

MIT License - see LICENSE file for details.