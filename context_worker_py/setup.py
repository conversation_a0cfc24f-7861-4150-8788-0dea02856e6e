#!/usr/bin/env python3
"""
Setup script for Context Worker Python
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Context Worker Python - Code analysis and context extraction tool"

# Read version from __init__.py
def get_version():
    init_path = os.path.join(os.path.dirname(__file__), '__init__.py')
    with open(init_path, 'r', encoding='utf-8') as f:
        for line in f:
            if line.startswith('__version__'):
                return line.split('=')[1].strip().strip('"\'')
    return "0.1.0"

setup(
    name="context-worker-py",
    version=get_version(),
    author="AutoDev Team",
    author_email="<EMAIL>",
    description="Code analysis and context extraction tool for various programming languages",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/autodev/context-worker-py",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Code Generators",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Text Processing :: Linguistic",
    ],
    python_requires=">=3.10",
    install_requires=[
        "tree-sitter>=0.20.0",
        "tree-sitter-python>=0.20.0",
        "tree-sitter-javascript>=0.20.0",
        "tree-sitter-typescript>=0.20.0",
        "tree-sitter-java>=0.20.0",
        "tree-sitter-go>=0.20.0",
        "tree-sitter-rust>=0.20.0",
        "tree-sitter-cpp>=0.20.0",
        "tree-sitter-c>=0.20.0",
        "aiofiles>=23.0.0",
        "pydantic>=2.0.0",
        "click>=8.0.0",
        "rich>=13.0.0",
        "pathspec>=0.11.0",
    ],
    extras_require={
        "cli": [
            "inquirer>=3.0.0",
            "colorama>=0.4.0",
        ],
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "isort>=5.12.0",
            "mypy>=1.0.0",
            "flake8>=6.0.0",
            "coverage>=7.0.0",
        ],
        "docs": [
            "sphinx>=6.0.0",
            "sphinx-rtd-theme>=1.2.0",
            "myst-parser>=1.0.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "context-worker=context_worker_py.main:main",
            "context-worker-finder=context_worker_py.examples.task_context_finder:main",
        ],
    },
    include_package_data=True,
    package_data={
        "context_worker_py": [
            "docs/*.md",
            "examples/*.py",
            "*.md",
        ],
    },
    project_urls={
        "Bug Reports": "https://github.com/autodev/context-worker-py/issues",
        "Source": "https://github.com/autodev/context-worker-py",
        "Documentation": "https://context-worker-py.readthedocs.io/",
    },
)
