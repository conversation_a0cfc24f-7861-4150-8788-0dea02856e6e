#!/usr/bin/env python3
"""
测试修复后的 Context Worker Python

验证所有导入问题是否已解决。
"""

import sys
import os

# 添加父目录到路径
current_dir = os.path.dirname(__file__)
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def test_basic_imports():
    """测试基础导入"""
    print("🔍 测试基础导入...")
    
    try:
        # 测试核心组件导入
        from context_worker_py import (
            AppConfig,
            AnalysisTypes,
            FileSystemScanner,
            CodeCollector,
            SymbolAnalyser
        )
        print("✅ 核心组件导入成功")
        
        # 测试示例导入
        from context_worker_py.examples import (
            ContextWorkerExample,
            TaskContextFinder,
            TaskContext
        )
        print("✅ 示例组件导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_functionality():
    """测试基础功能"""
    print("\n🧪 测试基础功能...")
    
    try:
        from context_worker_py import FileSystemScanner, CodeCollector
        from context_worker_py.examples import TaskContextFinder
        
        # 测试文件扫描器
        scanner = FileSystemScanner()
        print("✅ FileSystemScanner 创建成功")
        
        # 测试代码收集器
        collector = CodeCollector(".")
        print("✅ CodeCollector 创建成功")
        
        # 测试任务上下文查找器
        finder = TaskContextFinder(".")
        keywords = finder.extract_keywords("用户认证功能")
        print(f"✅ TaskContextFinder 创建成功，提取关键词: {keywords[:3]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 Context Worker Python 修复验证")
    print("=" * 50)
    
    # 测试基础导入
    if not test_basic_imports():
        print("\n❌ 基础导入测试失败")
        return False
    
    # 测试基础功能
    if not test_functionality():
        print("\n❌ 功能测试失败")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！")
    print("✅ Stack 导入问题已修复")
    print("✅ types 模块冲突已修复")
    print("✅ ast 模块冲突已修复")
    print("✅ Context Worker Python 可以正常使用")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
