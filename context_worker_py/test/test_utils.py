"""
Test utilities and helper functions
"""

import os
import time
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional, Callable, TypeVar, List
from unittest.mock import Mock, MagicMock


T = TypeVar('T')


class TestUtils:
    """Collection of test utility functions"""

    @staticmethod
    def create_mock_console() -> Mock:
        """Create a mock console that captures output"""
        return Mock(spec=['log', 'info', 'warn', 'error', 'debug'])

    @staticmethod
    async def wait(ms: int) -> None:
        """Wait for a specified amount of time"""
        await asyncio.sleep(ms / 1000.0)

    @staticmethod
    def create_mock_timer() -> Dict[str, Callable]:
        """Create a mock timer for testing timeouts"""
        # Note: Python doesn't have Je<PERSON>'s timer mocking,
        # this is a simplified version
        return {
            'advance': lambda ms: time.sleep(ms / 1000.0),
            'run_all': lambda: None,
            'restore': lambda: None
        }

    @staticmethod
    def create_mock_file_system() -> <PERSON>ck:
        """Create a mock file system for testing"""
        mock_fs = Mock()
        mock_fs.read_file = Mock(return_value="mock file content")
        mock_fs.write_file = Mock(return_value=True)
        mock_fs.exists = Mock(return_value=True)
        mock_fs.list_files = Mock(return_value=[])
        return mock_fs

    @staticmethod
    def create_mock_language_service() -> Mock:
        """Create a mock language service for testing"""
        mock_service = Mock()
        mock_service.get_language = Mock(return_value=None)
        mock_service.parse = Mock(return_value=None)
        mock_service.is_support_language = Mock(return_value=True)
        mock_service.ready = Mock(return_value=asyncio.Future())
        mock_service.ready.return_value.set_result(None)
        return mock_service

    @staticmethod
    def create_temp_file(content: str, suffix: str = '.tmp') -> str:
        """Create a temporary file with given content"""
        import tempfile

        with tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False) as f:
            f.write(content)
            return f.name

    @staticmethod
    def create_temp_directory() -> str:
        """Create a temporary directory"""
        import tempfile
        return tempfile.mkdtemp()

    @staticmethod
    def cleanup_temp_file(file_path: str) -> None:
        """Clean up a temporary file"""
        try:
            os.unlink(file_path)
        except OSError:
            pass

    @staticmethod
    def cleanup_temp_directory(dir_path: str) -> None:
        """Clean up a temporary directory"""
        import shutil
        try:
            shutil.rmtree(dir_path)
        except OSError:
            pass

    @staticmethod
    async def measure_execution_time(fn: Callable[[], T]) -> Dict[str, Any]:
        """Measure execution time of a function"""
        start = time.time()
        if asyncio.iscoroutinefunction(fn):
            result = await fn()
        else:
            result = fn()
        execution_time = time.time() - start

        return {
            'result': result,
            'time': execution_time * 1000  # Convert to milliseconds
        }

    @staticmethod
    def create_benchmark(name: str, iterations: int = 100) -> Callable:
        """Create a benchmark function"""
        def benchmark_decorator(fn: Callable) -> Callable:
            async def wrapper(*args, **kwargs):
                times = []
                for _ in range(iterations):
                    start = time.time()
                    if asyncio.iscoroutinefunction(fn):
                        await fn(*args, **kwargs)
                    else:
                        fn(*args, **kwargs)
                    times.append((time.time() - start) * 1000)

                avg_time = sum(times) / len(times)
                min_time = min(times)
                max_time = max(times)

                print(f"Benchmark '{name}':")
                print(f"  Iterations: {iterations}")
                print(f"  Average: {avg_time:.2f}ms")
                print(f"  Min: {min_time:.2f}ms")
                print(f"  Max: {max_time:.2f}ms")

                return {
                    'name': name,
                    'iterations': iterations,
                    'average': avg_time,
                    'min': min_time,
                    'max': max_time,
                    'times': times
                }

            return wrapper
        return benchmark_decorator

    @staticmethod
    def create_test_environment() -> Dict[str, Any]:
        """Create a test environment with common mocks"""
        mock_console = TestUtils.create_mock_console()
        mock_language_service = TestUtils.create_mock_language_service()
        mock_file_system = TestUtils.create_mock_file_system()

        return {
            'mock_console': mock_console,
            'mock_language_service': mock_language_service,
            'mock_file_system': mock_file_system,
            'cleanup': lambda: None  # Python doesn't need explicit mock cleanup
        }

    @staticmethod
    def assert_file_exists(file_path: str) -> None:
        """Assert that a file exists"""
        assert os.path.exists(file_path), f"File does not exist: {file_path}"

    @staticmethod
    def assert_directory_exists(dir_path: str) -> None:
        """Assert that a directory exists"""
        assert os.path.isdir(dir_path), f"Directory does not exist: {dir_path}"

    @staticmethod
    def assert_file_contains(file_path: str, content: str) -> None:
        """Assert that a file contains specific content"""
        TestUtils.assert_file_exists(file_path)
        with open(file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
        assert content in file_content, f"File does not contain expected content: {content}"

    @staticmethod
    def get_project_root() -> str:
        """Get the project root directory"""
        current_dir = Path(__file__).parent
        # Go up until we find the project root (contains setup.py or pyproject.toml)
        while current_dir.parent != current_dir:
            if (current_dir / 'setup.py').exists() or (current_dir / 'pyproject.toml').exists():
                return str(current_dir)
            current_dir = current_dir.parent

        # Fallback to current directory
        return str(Path.cwd())


class MockTreeSitterNode:
    """Mock tree-sitter node for testing"""

    def __init__(self, node_type: str, text: str, start_byte: int = 0, end_byte: int = 0):
        self.type = node_type
        self.text = text.encode('utf-8')
        self.start_byte = start_byte
        self.end_byte = end_byte or len(text)
        self.start_point = (0, start_byte)
        self.end_point = (0, self.end_byte)
        self.children = []
        self.parent = None

    def add_child(self, child: 'MockTreeSitterNode') -> None:
        """Add a child node"""
        child.parent = self
        self.children.append(child)


class TestLanguageServiceProvider:
    """Test-only language service provider for tree-sitter languages"""

    def __init__(self):
        self._parser = None

    async def parse(self, identifier: str, input_text: str):
        """Parse input text (mock implementation)"""
        return None

    async def get_language(self, lang_id: str):
        """Get language (mock implementation)"""
        # In a real test environment, this would load actual tree-sitter languages
        return None

    def get_parser(self):
        """Get parser instance"""
        return self._parser

    def is_support_language(self, identifier: str) -> bool:
        """Check if language is supported"""
        return True

    async def ready(self) -> None:
        """Wait for service to be ready"""
        pass

    def dispose(self) -> None:
        """Dispose of resources"""
        pass


# Custom assertion functions for testing
def assert_within_range(value: float, min_val: float, max_val: float) -> None:
    """Assert that a value is within a specified range"""
    assert min_val <= value <= max_val, f"Expected {value} to be within range {min_val} - {max_val}"


def assert_valid_scope_graph(scope_graph) -> None:
    """Assert that a scope graph is valid"""
    assert scope_graph is not None, "Scope graph should not be None"
    assert hasattr(scope_graph, 'nodes'), "Scope graph should have nodes"
    assert hasattr(scope_graph, 'edges'), "Scope graph should have edges"


def assert_valid_text_range(text_range) -> None:
    """Assert that a text range is valid"""
    assert text_range is not None, "Text range should not be None"
    assert hasattr(text_range, 'start'), "Text range should have start"
    assert hasattr(text_range, 'end'), "Text range should have end"
    assert text_range.start.byte <= text_range.end.byte, "Start byte should be <= end byte"