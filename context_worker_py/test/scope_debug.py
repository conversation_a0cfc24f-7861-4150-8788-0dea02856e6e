"""
Scope debugging utilities for analyzing scope graphs
"""

import json
from typing import List, TYPE_CHECKING

from ..code_search.scope_graph.model.text_range import TextRange
from ..code_search.scope_graph.model.symbol_id import name_of_symbol
from ..code_search.scope_graph.node.local_def import LocalDef
from ..code_search.scope_graph.edge.edge_kind import (
    DefToScope, ImportToScope, RefToDef, RefToImport, ScopeToScope
)

if TYPE_CHECKING:
    from ..code_search.scope_graph.scope_graph import ScopeGraph, NodeId
    from ..code_context.base.language_profile import LanguageProfile


class RefDebug:
    """Debug representation of a reference"""

    def __init__(self, context: str):
        self.context = context

    def __str__(self) -> str:
        return self.context

    def to_dict(self) -> dict:
        return {"context": self.context}


class DefDebug:
    """Debug representation of a definition"""

    def __init__(self, range: TextRange, name: str, refs: List[TextRange],
                 symbol: str, src: str):
        self.name = name
        self.range = range
        self.context = context_from_range(range, src)
        self.refs = [RefDebug(context_from_range(r, src)) for r in refs]
        self.symbol = symbol

    def __str__(self) -> str:
        return json.dumps({
            "name": self.name,
            "kind": self.symbol,
            "context": self.context,
            "references": [ref.to_dict() for ref in self.refs],
        })

    def to_dict(self) -> dict:
        return {
            "name": self.name,
            "kind": self.symbol,
            "context": self.context,
            "references": [ref.to_dict() for ref in self.refs],
        }


class ImportDebug:
    """Debug representation of an import"""

    def __init__(self, name: str, range: TextRange, refs: List[TextRange], src: str):
        self.name = name
        self.range = range
        self.context = context_from_range(range, src)
        self.refs = [RefDebug(context_from_range(r, src)) for r in refs]

    def __str__(self) -> str:
        return json.dumps({
            "name": self.name,
            "context": self.context,
            "references": [ref.to_dict() for ref in self.refs],
        })

    def to_dict(self) -> dict:
        return {
            "name": self.name,
            "context": self.context,
            "references": [ref.to_dict() for ref in self.refs],
        }


class ScopeDebug:
    """Debug representation of a scope"""

    def __init__(self, range: TextRange, language: 'LanguageProfile'):
        self.range = range
        self.defs: List[DefDebug] = []
        self.imports: List[ImportDebug] = []
        self.scopes: List['ScopeDebug'] = []
        self.language = language

    @classmethod
    def new(cls, scope_graph: 'ScopeGraph', start_node: 'NodeId',
            src: str, language: 'LanguageProfile') -> 'ScopeDebug':
        """Create a new ScopeDebug from a scope graph"""
        start_node_obj = scope_graph.get_node(start_node)
        if not start_node_obj:
            raise ValueError(f"Node {start_node} not found in scope graph")

        scope_debug = cls(start_node_obj.kind.range, language)
        scope_debug.build(scope_graph, start_node, src)
        return scope_debug

    def build(self, scope_graph: 'ScopeGraph', start_node: 'NodeId', src: str) -> None:
        """Build debug information from scope graph"""
        # Find definitions in this scope
        defs = []
        for edge_id in scope_graph.edges_to(start_node):
            edge = scope_graph.get_edge(edge_id)
            if edge and isinstance(edge.kind, DefToScope):
                def_node = scope_graph.get_node(edge.source)
                if def_node and isinstance(def_node.kind, LocalDef):
                    range_obj = def_node.kind.range
                    text = src[range_obj.start.byte:range_obj.end.byte]

                    # Find references to this definition
                    refs = []
                    for ref_edge_id in scope_graph.edges_to(edge.source):
                        ref_edge = scope_graph.get_edge(ref_edge_id)
                        if ref_edge and isinstance(ref_edge.kind, RefToDef):
                            ref_node = scope_graph.get_node(ref_edge.source)
                            if ref_node:
                                refs.append(ref_node.kind.range)

                    refs.sort(key=lambda r: r.start.byte)

                    # Get symbol name
                    symbol = "none"
                    if hasattr(def_node.kind, 'symbol_id') and def_node.kind.symbol_id:
                        symbol = name_of_symbol(scope_graph.namespaces, def_node.kind.symbol_id)

                    defs.append(DefDebug(range_obj, text, refs, symbol, src))

        # Find imports in this scope
        imports = []
        for edge_id in scope_graph.edges_to(start_node):
            edge = scope_graph.get_edge(edge_id)
            if edge and isinstance(edge.kind, ImportToScope):
                imp_node = scope_graph.get_node(edge.source)
                if imp_node:
                    range_obj = imp_node.kind.range
                    text = src[range_obj.start.byte:range_obj.end.byte]

                    # Find references to this import
                    refs = []
                    for ref_edge_id in scope_graph.edges_to(edge.source):
                        ref_edge = scope_graph.get_edge(ref_edge_id)
                        if ref_edge and isinstance(ref_edge.kind, RefToImport):
                            ref_node = scope_graph.get_node(ref_edge.source)
                            if ref_node:
                                refs.append(ref_node.kind.range)

                    refs.sort(key=lambda r: r.start.byte)
                    imports.append(ImportDebug(text, range_obj, refs, src))

        # Find child scopes
        scopes = []
        for edge_id in scope_graph.edges_to(start_node):
            edge = scope_graph.get_edge(edge_id)
            if edge and isinstance(edge.kind, ScopeToScope):
                source_scope = edge.source
                source_node = scope_graph.get_node(source_scope)
                if source_node:
                    scope_debug = ScopeDebug(source_node.kind.range, self.language)
                    scope_debug.build(scope_graph, source_scope, src)
                    scopes.append(scope_debug)

        # Sort by range
        self.defs = sorted(defs, key=lambda d: d.range.start.byte)
        self.imports = sorted(imports, key=lambda i: i.range.start.byte)
        self.scopes = sorted(scopes, key=lambda s: s.range.start.byte)

    def __str__(self) -> str:
        scopes_data = [scope.to_dict() for scope in self.scopes]
        return json.dumps({
            "definitions": [def_obj.to_dict() for def_obj in self.defs],
            "imports": [imp.to_dict() for imp in self.imports],
            "scopes": scopes_data,
        })

    def to_dict(self) -> dict:
        return {
            "definitions": [def_obj.to_dict() for def_obj in self.defs],
            "imports": [imp.to_dict() for imp in self.imports],
            "scopes": [scope.to_dict() for scope in self.scopes],
        }


def context_from_range(range_obj: TextRange, src: str) -> str:
    """Extract context from a text range"""
    # Find context start (beginning of line)
    context_start = range_obj.start.byte
    for i in range(range_obj.start.byte - 1, -1, -1):
        if src[i] == '\n':
            context_start = i + 1
            break
    else:
        context_start = 0

    # Find context end (end of line)
    context_end = range_obj.end.byte
    for i in range(range_obj.end.byte, len(src)):
        if src[i] == '\n':
            context_end = i
            break
    else:
        context_end = len(src)

    # Combine context with separator
    parts = [
        src[context_start:range_obj.start.byte].lstrip(),
        src[range_obj.start.byte:range_obj.end.byte],
        src[range_obj.end.byte:context_end].rstrip()
    ]

    return '§'.join(parts)