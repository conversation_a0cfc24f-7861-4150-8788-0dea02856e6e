"""
Setup script for context_worker_py
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8') if (this_directory / "README.md").exists() else ""

setup(
    name="context-worker-py",
    version="1.0.0",
    author="AutoDev Team",
    author_email="<EMAIL>",
    description="AutoDev Context Worker - Code analysis and context building tool",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/autodev/context-worker-py",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Code Generators",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=[
        "tree-sitter>=0.20.0",
        "tree-sitter-python>=0.20.0",
        "tree-sitter-typescript>=0.20.0",
        "tree-sitter-java>=0.20.0",
        "tree-sitter-javascript>=0.20.0",
        "aiofiles>=23.0.0",
        "pydantic>=2.0.0",
        "typing-extensions>=4.0.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "isort>=5.12.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "cli": [
            "inquirer>=3.0.0",
            "click>=8.0.0",
        ],
        "test": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-mock>=3.10.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "context-worker=context_worker_py.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "context_worker_py": [
            "**/*.scm",
            "**/*.json",
        ],
    },
    zip_safe=False,
)